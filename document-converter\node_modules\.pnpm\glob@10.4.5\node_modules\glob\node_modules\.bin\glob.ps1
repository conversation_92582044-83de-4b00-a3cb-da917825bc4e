#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="C:\Users\<USER>\Desktop\videcoding\anytools\document-converter\node_modules\.pnpm\glob@10.4.5\node_modules\glob\dist\esm\node_modules;C:\Users\<USER>\Desktop\videcoding\anytools\document-converter\node_modules\.pnpm\glob@10.4.5\node_modules\glob\dist\node_modules;C:\Users\<USER>\Desktop\videcoding\anytools\document-converter\node_modules\.pnpm\glob@10.4.5\node_modules\glob\node_modules;C:\Users\<USER>\Desktop\videcoding\anytools\document-converter\node_modules\.pnpm\glob@10.4.5\node_modules;C:\Users\<USER>\Desktop\videcoding\anytools\document-converter\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/proc/cygdrive/c/Users/<USER>/Desktop/videcoding/anytools/document-converter/node_modules/.pnpm/glob@10.4.5/node_modules/glob/dist/esm/node_modules:/proc/cygdrive/c/Users/<USER>/Desktop/videcoding/anytools/document-converter/node_modules/.pnpm/glob@10.4.5/node_modules/glob/dist/node_modules:/proc/cygdrive/c/Users/<USER>/Desktop/videcoding/anytools/document-converter/node_modules/.pnpm/glob@10.4.5/node_modules/glob/node_modules:/proc/cygdrive/c/Users/<USER>/Desktop/videcoding/anytools/document-converter/node_modules/.pnpm/glob@10.4.5/node_modules:/proc/cygdrive/c/Users/<USER>/Desktop/videcoding/anytools/document-converter/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

$ret=0
if (Test-Path "$basedir/node$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir/node$exe"  "$basedir/../../dist/esm/bin.mjs" $args
  } else {
    & "$basedir/node$exe"  "$basedir/../../dist/esm/bin.mjs" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "node$exe"  "$basedir/../../dist/esm/bin.mjs" $args
  } else {
    & "node$exe"  "$basedir/../../dist/esm/bin.mjs" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret
