{"name": "document-converter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "autoprefixer": "^10.4.21", "canvas": "^3.1.2", "clsx": "^2.1.1", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "jszip": "^3.10.1", "mammoth": "^1.9.1", "pdf-lib": "^1.17.1", "pdf2pic": "^3.2.0", "pdfjs-dist": "^5.3.93", "postcss": "^8.5.6", "pptx2json": "^0.0.10", "react": "^19.1.0", "react-dom": "^19.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/file-saver": "^2.0.7", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "tailwindcss": "^3.4.17", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}