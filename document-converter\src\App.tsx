
import Header from './components/Header'
import FileUploadArea from './components/FileUploadArea'
import FileList from './components/FileList'
import ConversionHistory from './components/ConversionHistory'
import NotificationSystem from './components/NotificationSystem'
import DownloadStatus from './components/DownloadStatus'
import { FileProvider } from './contexts/FileContext'
import { NotificationProvider, useNotification } from './contexts/NotificationContext'

const AppContent = () => {
  const { notifications, removeNotification } = useNotification()

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <main className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 主要转换区域 */}
          <div className="lg:col-span-2 space-y-6">
            <FileUploadArea />
            <FileList />
          </div>

          {/* 侧边栏 - 历史记录 */}
          <div className="lg:col-span-1">
            <ConversionHistory />
          </div>
        </div>
      </main>

      {/* 通知系统 */}
      <NotificationSystem
        notifications={notifications}
        onRemove={removeNotification}
      />

      {/* 下载状态 */}
      <DownloadStatus />
    </div>
  )
}

function App() {
  return (
    <NotificationProvider>
      <FileProvider>
        <AppContent />
      </FileProvider>
    </NotificationProvider>
  )
}

export default App
