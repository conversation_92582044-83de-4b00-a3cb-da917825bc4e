import { useState } from 'react'
import { 
  CogIcon, 
  PlayIcon,
  PauseIcon,
  StopIcon,
  CheckCircleIcon,
  ExclamationCircleIcon
} from '@heroicons/react/24/outline'
import { FileData, ConversionOptions } from '../types/file'
import { workerManager } from '../services/workerManager'

interface BatchProcessorProps {
  files: FileData[]
  onComplete: (results: { file: FileData; result?: File; error?: string }[]) => void
  onClose: () => void
}

interface BatchJob {
  file: FileData
  options: ConversionOptions
  status: 'pending' | 'processing' | 'completed' | 'error'
  progress: number
  result?: File
  error?: string
}

const BatchProcessor = ({ files, onComplete, onClose }: BatchProcessorProps) => {
  const [jobs, setJobs] = useState<BatchJob[]>([])
  const [isProcessing, setIsProcessing] = useState(false)
  const [isPaused, setIsPaused] = useState(false)
  const [currentJobIndex, setCurrentJobIndex] = useState(0)
  
  // 批量设置选项
  const [batchFormat, setBatchFormat] = useState<string>('pdf')
  const [batchQuality, setBatchQuality] = useState<'high' | 'standard' | 'fast'>('standard')
  const [concurrentJobs, setConcurrentJobs] = useState<number>(2)

  const initializeJobs = () => {
    const newJobs: BatchJob[] = files.map(file => ({
      file,
      options: {
        format: batchFormat,
        quality: batchQuality
      },
      status: 'pending',
      progress: 0
    }))
    setJobs(newJobs)
  }

  const startBatchProcessing = async () => {
    if (jobs.length === 0) {
      initializeJobs()
      return
    }

    setIsProcessing(true)
    setIsPaused(false)

    try {
      // 并发处理多个任务
      const activeJobs: Promise<void>[] = []
      let jobIndex = currentJobIndex

      while (jobIndex < jobs.length && !isPaused) {
        // 控制并发数量
        while (activeJobs.length < concurrentJobs && jobIndex < jobs.length) {
          const job = jobs[jobIndex]
          if (job.status === 'pending') {
            const jobPromise = processJob(jobIndex)
            activeJobs.push(jobPromise)
          }
          jobIndex++
        }

        // 等待至少一个任务完成
        if (activeJobs.length > 0) {
          await Promise.race(activeJobs)
          // 移除已完成的任务
          for (let i = activeJobs.length - 1; i >= 0; i--) {
            const job = activeJobs[i]
            if (await Promise.race([job, Promise.resolve('pending')]) !== 'pending') {
              activeJobs.splice(i, 1)
            }
          }
        }
      }

      // 等待所有剩余任务完成
      await Promise.all(activeJobs)

      if (!isPaused) {
        // 处理完成，返回结果
        const results = jobs.map(job => ({
          file: job.file,
          result: job.result,
          error: job.error
        }))
        onComplete(results)
      }
    } catch (error) {
      console.error('批量处理失败:', error)
    } finally {
      setIsProcessing(false)
    }
  }

  const processJob = async (jobIndex: number): Promise<void> => {
    const job = jobs[jobIndex]
    
    setJobs(prev => prev.map((j, i) => 
      i === jobIndex ? { ...j, status: 'processing', progress: 0 } : j
    ))

    try {
      const result = await workerManager.convertFile(
        job.file.file,
        job.options,
        (progress) => {
          setJobs(prev => prev.map((j, i) => 
            i === jobIndex ? { ...j, progress } : j
          ))
        }
      )

      setJobs(prev => prev.map((j, i) => 
        i === jobIndex ? { 
          ...j, 
          status: 'completed', 
          progress: 100, 
          result: Array.isArray(result) ? result[0] : result 
        } : j
      ))
    } catch (error) {
      setJobs(prev => prev.map((j, i) => 
        i === jobIndex ? { 
          ...j, 
          status: 'error', 
          error: error instanceof Error ? error.message : '处理失败' 
        } : j
      ))
    }
  }

  const pauseProcessing = () => {
    setIsPaused(true)
    setIsProcessing(false)
  }

  const stopProcessing = () => {
    setIsPaused(false)
    setIsProcessing(false)
    setCurrentJobIndex(0)
    setJobs([])
  }

  const getOverallProgress = () => {
    if (jobs.length === 0) return 0
    const totalProgress = jobs.reduce((sum, job) => sum + job.progress, 0)
    return Math.round(totalProgress / jobs.length)
  }

  const getStatusCounts = () => {
    return {
      pending: jobs.filter(j => j.status === 'pending').length,
      processing: jobs.filter(j => j.status === 'processing').length,
      completed: jobs.filter(j => j.status === 'completed').length,
      error: jobs.filter(j => j.status === 'error').length
    }
  }

  const statusCounts = getStatusCounts()

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900 flex items-center">
              <CogIcon className="h-6 w-6 mr-2" />
              批量处理 ({files.length} 个文件)
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>

          {/* 批量设置 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6 p-4 bg-gray-50 rounded-lg">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                目标格式
              </label>
              <select
                value={batchFormat}
                onChange={(e) => setBatchFormat(e.target.value)}
                disabled={isProcessing}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="pdf">PDF</option>
                <option value="jpg">JPG</option>
                <option value="png">PNG</option>
                <option value="webp">WebP</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                转换质量
              </label>
              <select
                value={batchQuality}
                onChange={(e) => setBatchQuality(e.target.value as any)}
                disabled={isProcessing}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="high">高质量</option>
                <option value="standard">标准</option>
                <option value="fast">快速</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                并发数量
              </label>
              <select
                value={concurrentJobs}
                onChange={(e) => setConcurrentJobs(Number(e.target.value))}
                disabled={isProcessing}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value={1}>1</option>
                <option value={2}>2</option>
                <option value={3}>3</option>
                <option value={4}>4</option>
              </select>
            </div>
          </div>

          {/* 进度概览 */}
          {jobs.length > 0 && (
            <div className="mb-6 p-4 bg-blue-50 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">总体进度</span>
                <span className="text-sm text-gray-600">{getOverallProgress()}%</span>
              </div>
              <div className="progress-bar mb-3">
                <div 
                  className="progress-fill"
                  style={{ width: `${getOverallProgress()}%` }}
                />
              </div>
              <div className="grid grid-cols-4 gap-4 text-sm">
                <div className="text-center">
                  <div className="text-lg font-semibold text-gray-600">{statusCounts.pending}</div>
                  <div className="text-gray-500">等待中</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-semibold text-blue-600">{statusCounts.processing}</div>
                  <div className="text-gray-500">处理中</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-semibold text-green-600">{statusCounts.completed}</div>
                  <div className="text-gray-500">已完成</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-semibold text-red-600">{statusCounts.error}</div>
                  <div className="text-gray-500">失败</div>
                </div>
              </div>
            </div>
          )}

          {/* 任务列表 */}
          <div className="max-h-64 overflow-y-auto mb-6">
            {jobs.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                点击"开始处理"初始化批量任务
              </div>
            ) : (
              <div className="space-y-2">
                {jobs.map((job, index) => (
                  <div
                    key={job.file.id}
                    className={`p-3 rounded-lg border ${
                      job.status === 'completed' ? 'bg-green-50 border-green-200' :
                      job.status === 'error' ? 'bg-red-50 border-red-200' :
                      job.status === 'processing' ? 'bg-blue-50 border-blue-200' :
                      'bg-gray-50 border-gray-200'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="flex-shrink-0">
                          {job.status === 'completed' && <CheckCircleIcon className="h-5 w-5 text-green-500" />}
                          {job.status === 'error' && <ExclamationCircleIcon className="h-5 w-5 text-red-500" />}
                          {job.status === 'processing' && <CogIcon className="h-5 w-5 text-blue-500 animate-spin" />}
                          {job.status === 'pending' && <div className="h-5 w-5 rounded-full bg-gray-300" />}
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-900">{job.file.name}</p>
                          <p className="text-xs text-gray-500">
                            {job.error || `${job.file.type} → ${job.options.format}`}
                          </p>
                        </div>
                      </div>
                      
                      <div className="text-right">
                        <div className="text-sm font-medium text-gray-900">{job.progress}%</div>
                        {job.status === 'processing' && (
                          <div className="w-16 progress-bar mt-1">
                            <div 
                              className="progress-fill"
                              style={{ width: `${job.progress}%` }}
                            />
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* 控制按钮 */}
          <div className="flex justify-between">
            <button
              onClick={onClose}
              className="btn-secondary"
            >
              关闭
            </button>
            
            <div className="flex space-x-2">
              {!isProcessing && !isPaused && (
                <button
                  onClick={startBatchProcessing}
                  className="btn-primary flex items-center"
                >
                  <PlayIcon className="h-4 w-4 mr-1" />
                  开始处理
                </button>
              )}
              
              {isProcessing && (
                <button
                  onClick={pauseProcessing}
                  className="btn-secondary flex items-center"
                >
                  <PauseIcon className="h-4 w-4 mr-1" />
                  暂停
                </button>
              )}
              
              {(isProcessing || isPaused) && (
                <button
                  onClick={stopProcessing}
                  className="btn-secondary flex items-center"
                >
                  <StopIcon className="h-4 w-4 mr-1" />
                  停止
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default BatchProcessor
