import { useState, useEffect } from 'react'
import { 
  ArrowsRightLeftIcon,
  XMarkIcon,
  ArrowsPointingOutIcon,
  ArrowsPointingInIcon,
  DocumentIcon,
  PhotoIcon
} from '@heroicons/react/24/outline'
import type { FileData } from '../types/file'

interface ComparisonViewProps {
  originalFile: FileData
  convertedFile: File
  onClose: () => void
}

const ComparisonView = ({ originalFile, convertedFile, onClose }: ComparisonViewProps) => {
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [originalPreview, setOriginalPreview] = useState<string | null>(null)
  const [convertedPreview, setConvertedPreview] = useState<string | null>(null)
  const [viewMode, setViewMode] = useState<'side-by-side' | 'overlay'>('side-by-side')

  useEffect(() => {
    generatePreview(originalFile.file, setOriginalPreview)
    generatePreview(convertedFile, setConvertedPreview)

    return () => {
      if (originalPreview) URL.revokeObjectURL(originalPreview)
      if (convertedPreview) URL.revokeObjectURL(convertedPreview)
    }
  }, [originalFile, convertedFile])

  const generatePreview = async (file: File, setPreview: (url: string | null) => void) => {
    try {
      if (file.type.startsWith('image/')) {
        const url = URL.createObjectURL(file)
        setPreview(url)
      } else if (file.type === 'application/pdf') {
        const url = URL.createObjectURL(file)
        setPreview(url)
      } else {
        setPreview(null)
      }
    } catch (error) {
      console.error('生成预览失败:', error)
      setPreview(null)
    }
  }

  const renderFilePreview = (preview: string | null, file: File, title: string) => {
    if (!preview) {
      return (
        <div className="flex flex-col items-center justify-center h-64 bg-gray-100 rounded-lg">
          <DocumentIcon className="h-16 w-16 text-gray-400 mb-4" />
          <p className="text-gray-600 text-center">无法预览此文件类型</p>
          <p className="text-sm text-gray-500 mt-2">{file.name}</p>
        </div>
      )
    }

    if (file.type.startsWith('image/')) {
      return (
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900 text-center">{title}</h3>
          <div className="flex justify-center">
            <img
              src={preview}
              alt={file.name}
              className="max-w-full max-h-80 object-contain rounded-lg shadow-sm border border-gray-200"
            />
          </div>
        </div>
      )
    }

    if (file.type === 'application/pdf') {
      return (
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900 text-center">{title}</h3>
          <iframe
            src={preview}
            className="w-full h-80 border border-gray-300 rounded-lg"
            title={`PDF预览: ${file.name}`}
          />
        </div>
      )
    }

    return (
      <div className="flex flex-col items-center justify-center h-64 bg-gray-100 rounded-lg">
        <DocumentIcon className="h-16 w-16 text-gray-400 mb-4" />
        <p className="text-gray-600">预览不可用</p>
      </div>
    )
  }

  const getFileStats = (original: File, converted: File) => {
    const sizeReduction = ((original.size - converted.size) / original.size) * 100
    const compressionRatio = converted.size / original.size

    return {
      originalSize: (original.size / 1024 / 1024).toFixed(2),
      convertedSize: (converted.size / 1024 / 1024).toFixed(2),
      sizeReduction: sizeReduction.toFixed(1),
      compressionRatio: compressionRatio.toFixed(2),
      isSmaller: converted.size < original.size
    }
  }

  const stats = getFileStats(originalFile.file, convertedFile)

  return (
    <div className={`fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 ${
      isFullscreen ? 'p-0' : 'p-4'
    }`}>
      <div className={`bg-white rounded-xl shadow-xl ${
        isFullscreen ? 'w-full h-full rounded-none' : 'max-w-7xl w-full max-h-[95vh]'
      } overflow-hidden`}>
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-4">
            <h2 className="text-xl font-semibold text-gray-900 flex items-center">
              <ArrowsRightLeftIcon className="h-6 w-6 mr-2" />
              文件对比
            </h2>
            
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setViewMode('side-by-side')}
                className={`px-3 py-1 text-sm rounded-md transition-colors ${
                  viewMode === 'side-by-side'
                    ? 'bg-primary-100 text-primary-700'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                并排对比
              </button>
              <button
                onClick={() => setViewMode('overlay')}
                className={`px-3 py-1 text-sm rounded-md transition-colors ${
                  viewMode === 'overlay'
                    ? 'bg-primary-100 text-primary-700'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                叠加对比
              </button>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setIsFullscreen(!isFullscreen)}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              title={isFullscreen ? '退出全屏' : '全屏显示'}
            >
              {isFullscreen ? (
                <ArrowsPointingInIcon className="h-5 w-5" />
              ) : (
                <ArrowsPointingOutIcon className="h-5 w-5" />
              )}
            </button>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <XMarkIcon className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* 统计信息 */}
        <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div className="text-center">
              <p className="text-gray-500">原文件大小</p>
              <p className="font-semibold text-gray-900">{stats.originalSize} MB</p>
            </div>
            <div className="text-center">
              <p className="text-gray-500">转换后大小</p>
              <p className="font-semibold text-gray-900">{stats.convertedSize} MB</p>
            </div>
            <div className="text-center">
              <p className="text-gray-500">大小变化</p>
              <p className={`font-semibold ${
                stats.isSmaller ? 'text-green-600' : 'text-red-600'
              }`}>
                {stats.isSmaller ? '-' : '+'}{Math.abs(parseFloat(stats.sizeReduction))}%
              </p>
            </div>
            <div className="text-center">
              <p className="text-gray-500">压缩比</p>
              <p className="font-semibold text-gray-900">{stats.compressionRatio}:1</p>
            </div>
          </div>
        </div>

        {/* 预览内容 */}
        <div className={`p-6 ${isFullscreen ? 'h-full overflow-y-auto' : 'max-h-96 overflow-y-auto'}`}>
          {viewMode === 'side-by-side' ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div>
                {renderFilePreview(originalPreview, originalFile.file, '原文件')}
              </div>
              <div>
                {renderFilePreview(convertedPreview, convertedFile, '转换后')}
              </div>
            </div>
          ) : (
            <div className="relative">
              <div className="absolute inset-0 opacity-50">
                {renderFilePreview(originalPreview, originalFile.file, '原文件 (背景)')}
              </div>
              <div className="relative z-10">
                {renderFilePreview(convertedPreview, convertedFile, '转换后 (前景)')}
              </div>
            </div>
          )}
        </div>

        {/* 文件详细信息 */}
        <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                {originalFile.type.startsWith('image/') ? (
                  <PhotoIcon className="h-5 w-5 mr-2" />
                ) : (
                  <DocumentIcon className="h-5 w-5 mr-2" />
                )}
                原文件详情
              </h4>
              <div className="space-y-2 text-sm text-gray-600">
                <div className="flex justify-between">
                  <span>文件名:</span>
                  <span className="font-medium">{originalFile.name}</span>
                </div>
                <div className="flex justify-between">
                  <span>文件类型:</span>
                  <span className="font-medium">{originalFile.type}</span>
                </div>
                <div className="flex justify-between">
                  <span>文件大小:</span>
                  <span className="font-medium">{stats.originalSize} MB</span>
                </div>
              </div>
            </div>
            
            <div>
              <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                {convertedFile.type.startsWith('image/') ? (
                  <PhotoIcon className="h-5 w-5 mr-2" />
                ) : (
                  <DocumentIcon className="h-5 w-5 mr-2" />
                )}
                转换后详情
              </h4>
              <div className="space-y-2 text-sm text-gray-600">
                <div className="flex justify-between">
                  <span>文件名:</span>
                  <span className="font-medium">{convertedFile.name}</span>
                </div>
                <div className="flex justify-between">
                  <span>文件类型:</span>
                  <span className="font-medium">{convertedFile.type}</span>
                </div>
                <div className="flex justify-between">
                  <span>文件大小:</span>
                  <span className="font-medium">{stats.convertedSize} MB</span>
                </div>
                <div className="flex justify-between">
                  <span>转换状态:</span>
                  <span className="font-medium text-green-600">成功</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ComparisonView
