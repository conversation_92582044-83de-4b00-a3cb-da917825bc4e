import { useState, useEffect } from 'react'
import {
  ClockIcon,
  TrashIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  ChartBarIcon,
  ArrowDownTrayIcon,
  ArrowUpTrayIcon
} from '@heroicons/react/24/outline'
import { HistoryService, ConversionHistoryItem } from '../services/historyService'

const ConversionHistory = () => {
  const [historyItems, setHistoryItems] = useState<ConversionHistoryItem[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [filterStatus, setFilterStatus] = useState<'all' | 'completed' | 'failed'>('all')
  const [showStats, setShowStats] = useState(false)

  useEffect(() => {
    loadHistory()
  }, [])

  const loadHistory = () => {
    let items = HistoryService.getHistory()

    // 应用搜索过滤
    if (searchQuery) {
      items = HistoryService.searchHistory(searchQuery)
    }

    // 应用状态过滤
    if (filterStatus !== 'all') {
      items = items.filter(item => item.status === filterStatus)
    }

    setHistoryItems(items.slice(0, 10)) // 只显示最近10条
  }

  useEffect(() => {
    loadHistory()
  }, [searchQuery, filterStatus])

  const formatTimeAgo = (timestamp: Date) => {
    const now = new Date()
    const diffInMinutes = Math.floor((now.getTime() - timestamp.getTime()) / (1000 * 60))

    if (diffInMinutes < 1) return '刚刚'
    if (diffInMinutes < 60) return `${diffInMinutes}分钟前`

    const diffInHours = Math.floor(diffInMinutes / 60)
    if (diffInHours < 24) return `${diffInHours}小时前`

    const diffInDays = Math.floor(diffInHours / 24)
    return `${diffInDays}天前`
  }

  const handleClearHistory = () => {
    if (confirm('确定要清空所有历史记录吗？此操作不可撤销。')) {
      HistoryService.clearHistory()
      loadHistory()
    }
  }

  const handleRemoveItem = (id: string) => {
    HistoryService.removeHistoryItem(id)
    loadHistory()
  }

  const handleExportHistory = () => {
    const data = HistoryService.exportHistory()
    const blob = new Blob([data], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `conversion-history-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  const handleImportHistory = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      const content = e.target?.result as string
      if (HistoryService.importHistory(content)) {
        loadHistory()
        alert('历史记录导入成功！')
      } else {
        alert('历史记录导入失败，请检查文件格式。')
      }
    }
    reader.readAsText(file)
    event.target.value = '' // 清空input
  }

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold text-gray-900 flex items-center">
          <ClockIcon className="h-5 w-5 mr-2" />
          转换历史
        </h2>

        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowStats(!showStats)}
            className="text-sm text-gray-500 hover:text-primary-600 transition-colors"
            title="统计信息"
          >
            <ChartBarIcon className="h-4 w-4" />
          </button>

          <button
            onClick={handleClearHistory}
            className="text-sm text-gray-500 hover:text-red-500 transition-colors"
            title="清空历史"
          >
            <TrashIcon className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* 搜索和过滤 */}
      <div className="mb-4 space-y-3">
        <div className="relative">
          <MagnifyingGlassIcon className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="搜索文件名或格式..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <FunnelIcon className="h-4 w-4 text-gray-400" />
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value as any)}
              className="text-sm border border-gray-300 rounded px-2 py-1"
            >
              <option value="all">全部</option>
              <option value="completed">成功</option>
              <option value="failed">失败</option>
            </select>
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={handleExportHistory}
              className="text-xs text-gray-500 hover:text-primary-600 transition-colors flex items-center"
              title="导出历史"
            >
              <ArrowDownTrayIcon className="h-3 w-3 mr-1" />
              导出
            </button>

            <label className="text-xs text-gray-500 hover:text-primary-600 transition-colors flex items-center cursor-pointer">
              <ArrowUpTrayIcon className="h-3 w-3 mr-1" />
              导入
              <input
                type="file"
                accept=".json"
                onChange={handleImportHistory}
                className="hidden"
              />
            </label>
          </div>
        </div>
      </div>
      
      {historyItems.length === 0 ? (
        <div className="text-center text-gray-500 py-8">
          <ClockIcon className="h-12 w-12 mx-auto mb-3 text-gray-300" />
          <p>{searchQuery || filterStatus !== 'all' ? '没有找到匹配的记录' : '暂无转换历史'}</p>
        </div>
      ) : (
        <div className="space-y-3">
          {historyItems.map((item) => (
            <div key={item.id} className="border border-gray-100 rounded-lg p-3 hover:bg-gray-50 transition-colors">
              <div className="flex items-center justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {item.fileName}
                    </p>
                    <span className={`text-xs px-2 py-1 rounded ${
                      item.status === 'completed'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {item.status === 'completed' ? '成功' : '失败'}
                    </span>
                  </div>

                  <div className="flex items-center space-x-2 mt-1">
                    <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                      {item.fromFormat}
                    </span>
                    <span className="text-xs text-gray-400">→</span>
                    <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                      {item.toFormat}
                    </span>
                    <span className="text-xs text-gray-500">
                      {(item.processingTime / 1000).toFixed(1)}s
                    </span>
                  </div>

                  <div className="flex items-center justify-between mt-1">
                    <p className="text-xs text-gray-500">
                      {formatTimeAgo(item.timestamp)}
                    </p>
                    {item.status === 'completed' && (
                      <p className="text-xs text-gray-500">
                        {(item.originalSize / 1024 / 1024).toFixed(2)} MB → {(item.convertedSize / 1024 / 1024).toFixed(2)} MB
                      </p>
                    )}
                  </div>

                  {item.error && (
                    <p className="text-xs text-red-600 mt-1 truncate">
                      {item.error}
                    </p>
                  )}
                </div>

                <div className="flex items-center space-x-2 ml-4">
                  {item.status === 'completed' && (
                    <button className="text-xs text-primary-600 hover:text-primary-700 font-medium">
                      重新下载
                    </button>
                  )}
                  <button
                    onClick={() => handleRemoveItem(item.id)}
                    className="text-xs text-gray-400 hover:text-red-500 transition-colors"
                    title="删除记录"
                  >
                    <TrashIcon className="h-3 w-3" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
      
      <div className="mt-4 pt-4 border-t border-gray-100">
        <div className="text-xs text-gray-500 space-y-1">
          <p>• 历史记录仅保存在本地</p>
          <p>• 文件不会上传到服务器</p>
          <p>• 清除浏览器数据会删除历史</p>
        </div>
      </div>
    </div>
  )
}

export default ConversionHistory
