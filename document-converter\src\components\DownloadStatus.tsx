import { useState, useEffect } from 'react'
import { 
  ArrowDownTrayIcon,
  CheckCircleIcon,
  ExclamationCircleIcon,
  XMarkIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline'
import { DownloadManager, DownloadItem } from '../services/downloadManager'

const DownloadStatus = () => {
  const [downloads, setDownloads] = useState<DownloadItem[]>([])
  const [isExpanded, setIsExpanded] = useState(false)

  useEffect(() => {
    const unsubscribe = DownloadManager.addListener(setDownloads)
    return unsubscribe
  }, [])

  const activeDownloads = downloads.filter(d => d.status !== 'completed')
  const stats = DownloadManager.getDownloadStats()

  if (downloads.length === 0) {
    return null
  }

  const getStatusIcon = (status: DownloadItem['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="h-4 w-4 text-green-500" />
      case 'error':
        return <ExclamationCircleIcon className="h-4 w-4 text-red-500" />
      case 'downloading':
        return <ArrowDownTrayIcon className="h-4 w-4 text-blue-500 animate-pulse" />
      default:
        return <ArrowDownTrayIcon className="h-4 w-4 text-gray-400" />
    }
  }

  const getStatusColor = (status: DownloadItem['status']) => {
    switch (status) {
      case 'completed':
        return 'bg-green-50 border-green-200'
      case 'error':
        return 'bg-red-50 border-red-200'
      case 'downloading':
        return 'bg-blue-50 border-blue-200'
      default:
        return 'bg-gray-50 border-gray-200'
    }
  }

  return (
    <div className="fixed bottom-4 right-4 z-40 max-w-sm">
      {/* 下载状态摘要 */}
      <div 
        className="bg-white rounded-lg shadow-lg border border-gray-200 p-4 cursor-pointer"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <ArrowDownTrayIcon className="h-5 w-5 text-primary-600" />
            <div>
              <p className="text-sm font-medium text-gray-900">
                下载管理器
              </p>
              <p className="text-xs text-gray-500">
                {stats.downloading > 0 && `${stats.downloading} 个正在下载`}
                {stats.completed > 0 && `${stats.completed} 个已完成`}
                {stats.failed > 0 && `${stats.failed} 个失败`}
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {stats.downloading > 0 && (
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
            )}
            <button
              onClick={(e) => {
                e.stopPropagation()
                DownloadManager.clearCompletedDownloads()
              }}
              className="text-xs text-gray-400 hover:text-gray-600"
              title="清除已完成"
            >
              清除
            </button>
          </div>
        </div>
      </div>

      {/* 展开的下载列表 */}
      {isExpanded && (
        <div className="mt-2 bg-white rounded-lg shadow-lg border border-gray-200 max-h-80 overflow-y-auto">
          <div className="p-3 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium text-gray-900">下载列表</h3>
              <button
                onClick={() => setIsExpanded(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="h-4 w-4" />
              </button>
            </div>
          </div>
          
          <div className="divide-y divide-gray-100">
            {downloads.map((download) => (
              <div
                key={download.id}
                className={`p-3 ${getStatusColor(download.status)}`}
              >
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0">
                    {getStatusIcon(download.status)}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {download.name}
                    </p>
                    
                    {download.status === 'downloading' && (
                      <div className="mt-1">
                        <div className="flex items-center justify-between text-xs text-gray-500 mb-1">
                          <span>下载中...</span>
                          <span>{download.progress}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-1">
                          <div
                            className="bg-blue-500 h-1 rounded-full transition-all duration-300"
                            style={{ width: `${download.progress}%` }}
                          />
                        </div>
                      </div>
                    )}
                    
                    {download.status === 'completed' && (
                      <p className="text-xs text-green-600 mt-1">下载完成</p>
                    )}
                    
                    {download.status === 'error' && (
                      <p className="text-xs text-red-600 mt-1 truncate">
                        {download.error}
                      </p>
                    )}
                  </div>
                  
                  <div className="flex items-center space-x-1">
                    {download.status === 'error' && (
                      <button
                        onClick={() => DownloadManager.retryDownload(download.id)}
                        className="text-xs text-blue-600 hover:text-blue-700 p-1"
                        title="重试"
                      >
                        <ArrowPathIcon className="h-3 w-3" />
                      </button>
                    )}
                    
                    {download.status === 'downloading' && (
                      <button
                        onClick={() => DownloadManager.cancelDownload(download.id)}
                        className="text-xs text-red-600 hover:text-red-700 p-1"
                        title="取消"
                      >
                        <XMarkIcon className="h-3 w-3" />
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          {downloads.length > 0 && (
            <div className="p-3 border-t border-gray-200 bg-gray-50">
              <div className="flex items-center justify-between text-xs text-gray-500">
                <span>总计: {downloads.length} 个下载</span>
                <button
                  onClick={() => DownloadManager.clearAllDownloads()}
                  className="text-red-600 hover:text-red-700"
                >
                  清空全部
                </button>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default DownloadStatus
