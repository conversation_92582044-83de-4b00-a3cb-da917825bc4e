import { useState } from 'react'
import {
  DocumentIcon,
  PhotoIcon,
  TrashIcon,
  ArrowDownTrayIcon,
  Cog6ToothIcon,
  CheckCircleIcon,
  ExclamationCircleIcon,
  PencilIcon
} from '@heroicons/react/24/outline'
import { FileData, ConversionStatus } from '../types/file'
import { useFileContext } from '../contexts/FileContext'
import ImageEditor from './ImageEditor'

interface FileItemProps {
  file: FileData
  onRemove: () => void
}

const FileItem = ({ file, onRemove }: FileItemProps) => {
  const [selectedFormat, setSelectedFormat] = useState('')
  const [showImageEditor, setShowImageEditor] = useState(false)
  const { convertFile, updateFileStatus } = useFileContext()

  const getFileIcon = (type: string) => {
    if (type.startsWith('image/')) {
      return <PhotoIcon className="h-8 w-8 text-blue-500" />
    }
    return <DocumentIcon className="h-8 w-8 text-gray-500" />
  }

  const getStatusIcon = (status: ConversionStatus) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />
      case 'error':
        return <ExclamationCircleIcon className="h-5 w-5 text-red-500" />
      case 'processing':
        return <Cog6ToothIcon className="h-5 w-5 text-blue-500 animate-spin" />
      default:
        return null
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getAvailableFormats = (fileType: string) => {
    if (fileType.includes('pdf')) {
      return ['word', 'excel', 'powerpoint', 'jpg', 'png']
    }
    if (fileType.includes('image')) {
      return ['pdf', 'jpg', 'png', 'webp', 'svg']
    }
    if (fileType.includes('word') || fileType.includes('document')) {
      return ['pdf', 'jpg', 'png']
    }
    if (fileType.includes('excel') || fileType.includes('spreadsheet')) {
      return ['pdf', 'jpg', 'png']
    }
    if (fileType.includes('powerpoint') || fileType.includes('presentation')) {
      return ['pdf', 'jpg', 'png']
    }
    return ['pdf']
  }

  const handleConvert = async () => {
    if (!selectedFormat) return

    try {
      await convertFile(file.id, {
        format: selectedFormat,
        quality: 'standard'
      })
    } catch (error) {
      console.error('转换失败:', error)
    }
  }

  const handleImageEdit = (editedFile: File) => {
    // 更新文件数据
    updateFileStatus(file.id, 'completed', 100)
    setShowImageEditor(false)

    // 这里可以添加更多的处理逻辑，比如更新文件列表
    console.log('图片编辑完成:', editedFile)
  }

  const isImage = file.type.startsWith('image/')

  return (
    <div className="file-item">
      <div className="flex items-center space-x-4">
        <div className="flex-shrink-0">
          {getFileIcon(file.type)}
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2">
            <p className="text-sm font-medium text-gray-900 truncate">
              {file.name}
            </p>
            {getStatusIcon(file.status)}
          </div>
          
          <div className="flex items-center space-x-4 mt-1">
            <p className="text-xs text-gray-500">
              {formatFileSize(file.size)}
            </p>
            <p className="text-xs text-gray-500">
              {file.type}
            </p>
          </div>
          
          {file.status === 'processing' && (
            <div className="mt-2">
              <div className="progress-bar">
                <div 
                  className="progress-fill"
                  style={{ width: `${file.progress || 0}%` }}
                />
              </div>
              <p className="text-xs text-gray-500 mt-1">
                转换中... {file.progress || 0}%
              </p>
            </div>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          <select
            value={selectedFormat}
            onChange={(e) => setSelectedFormat(e.target.value)}
            className="text-sm border border-gray-300 rounded px-2 py-1"
            disabled={file.status === 'processing'}
          >
            <option value="">选择格式</option>
            {getAvailableFormats(file.type).map((format) => (
              <option key={format} value={format}>
                {format.toUpperCase()}
              </option>
            ))}
          </select>
          
          <button
            onClick={handleConvert}
            className="btn-primary text-xs px-3 py-1"
            disabled={!selectedFormat || file.status === 'processing'}
          >
            转换
          </button>

          {isImage && file.status !== 'processing' && (
            <button
              onClick={() => setShowImageEditor(true)}
              className="btn-secondary text-xs px-3 py-1"
              title="编辑图片"
            >
              <PencilIcon className="h-3 w-3 mr-1" />
              编辑
            </button>
          )}
          
          {file.status === 'completed' && file.convertedFile && (
            <button
              onClick={() => {
                if (file.convertedFile) {
                  const link = document.createElement('a')
                  link.href = URL.createObjectURL(file.convertedFile)
                  link.download = file.convertedFile.name
                  document.body.appendChild(link)
                  link.click()
                  document.body.removeChild(link)
                  URL.revokeObjectURL(link.href)
                }
              }}
              className="btn-secondary text-xs px-3 py-1"
            >
              <ArrowDownTrayIcon className="h-3 w-3 mr-1" />
              下载
            </button>
          )}
          
          <button
            onClick={onRemove}
            className="p-1 text-gray-400 hover:text-red-500 transition-colors"
          >
            <TrashIcon className="h-4 w-4" />
          </button>
        </div>
      </div>

      {showImageEditor && (
        <ImageEditor
          file={file}
          onSave={handleImageEdit}
          onClose={() => setShowImageEditor(false)}
        />
      )}
    </div>
  )
}

export default FileItem
