import { useState } from 'react'
import { TrashIcon, ArrowDownTrayIcon, ArrowsPointingInIcon, CogIcon } from '@heroicons/react/24/outline'
import { useFileContext } from '../contexts/FileContext'
import { useNotification } from '../contexts/NotificationContext'
import { DownloadManager } from '../services/downloadManager'
import FileItem from './FileItem'
import FileMerger from './FileMerger'
import BatchProcessor from './BatchProcessor'

const FileList = () => {
  const [showMerger, setShowMerger] = useState(false)
  const [showBatchProcessor, setShowBatchProcessor] = useState(false)
  const { files, removeFile, clearAllFiles, addFiles } = useFileContext()
  const { showSuccess, showError } = useNotification()

  const handleMergeComplete = (mergedFile: File) => {
    addFiles([mergedFile])
    setShowMerger(false)
  }

  const handleBatchComplete = (results: { file: any; result?: File; error?: string }[]) => {
    const successfulResults = results.filter(r => r.result).map(r => r.result!)
    if (successfulResults.length > 0) {
      addFiles(successfulResults)
    }
    setShowBatchProcessor(false)
  }

  const handleBatchDownload = async () => {
    const completedFiles = files.filter((f: any) => f.status === 'completed' && f.convertedFile)

    if (completedFiles.length === 0) {
      showError('没有可下载的文件', '请先完成文件转换')
      return
    }

    try {
      const filesToDownload = completedFiles.map((f: any) => ({
        file: f.convertedFile,
        name: f.convertedFile.name
      }))

      await DownloadManager.downloadMultipleFiles(
        filesToDownload,
        `converted_files_${new Date().toISOString().split('T')[0]}.zip`
      )

      showSuccess('开始下载', `正在打包下载 ${completedFiles.length} 个文件`)
    } catch (error) {
      showError('下载失败', '批量下载时发生错误')
    }
  }

  const canMerge = files.filter((f: any) => f.type === 'application/pdf').length >= 2 ||
                   files.filter((f: any) => f.type.startsWith('image/')).length >= 2

  const hasCompletedFiles = files.some((f: any) => f.status === 'completed' && f.convertedFile)

  if (files.length === 0) {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
        <div className="text-center text-gray-500">
          <p className="text-lg">暂无文件</p>
          <p className="text-sm mt-1">请上传文件开始转换</p>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold text-gray-900">
          文件列表 ({files.length})
        </h2>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowBatchProcessor(true)}
            disabled={files.length === 0}
            className="btn-primary text-sm"
            title="批量处理"
          >
            <CogIcon className="h-4 w-4 mr-1" />
            批量处理
          </button>

          <button
            onClick={() => setShowMerger(true)}
            disabled={!canMerge}
            className="btn-secondary text-sm"
            title="合并文件"
          >
            <ArrowsPointingInIcon className="h-4 w-4 mr-1" />
            合并
          </button>

          <button
            onClick={clearAllFiles}
            className="btn-secondary text-sm"
          >
            <TrashIcon className="h-4 w-4 mr-1" />
            清空全部
          </button>

          <button
            onClick={handleBatchDownload}
            disabled={!hasCompletedFiles}
            className="btn-primary text-sm"
            title="批量下载已转换的文件"
          >
            <ArrowDownTrayIcon className="h-4 w-4 mr-1" />
            批量下载
          </button>
        </div>
      </div>
      
      <div className="space-y-3">
        {files.map((file) => (
          <FileItem
            key={file.id}
            file={file}
            onRemove={() => removeFile(file.id)}
          />
        ))}
      </div>

      {showMerger && (
        <FileMerger
          files={files}
          onMergeComplete={handleMergeComplete}
          onClose={() => setShowMerger(false)}
        />
      )}

      {showBatchProcessor && (
        <BatchProcessor
          files={files}
          onComplete={handleBatchComplete}
          onClose={() => setShowBatchProcessor(false)}
        />
      )}
    </div>
  )
}

export default FileList
