import { useState } from 'react'
import { TrashIcon, ArrowDownTrayIcon, ArrowsPointingInIcon, CogIcon } from '@heroicons/react/24/outline'
import { useFileContext } from '../contexts/FileContext'
import FileItem from './FileItem'
import FileMerger from './FileMerger'
import BatchProcessor from './BatchProcessor'

const FileList = () => {
  const [showMerger, setShowMerger] = useState(false)
  const { files, removeFile, clearAllFiles, addFiles } = useFileContext()

  const handleMergeComplete = (mergedFile: File) => {
    addFiles([mergedFile])
    setShowMerger(false)
  }

  const canMerge = files.filter(f => f.type === 'application/pdf').length >= 2 ||
                   files.filter(f => f.type.startsWith('image/')).length >= 2

  if (files.length === 0) {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
        <div className="text-center text-gray-500">
          <p className="text-lg">暂无文件</p>
          <p className="text-sm mt-1">请上传文件开始转换</p>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold text-gray-900">
          文件列表 ({files.length})
        </h2>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowMerger(true)}
            disabled={!canMerge}
            className="btn-secondary text-sm"
            title="合并文件"
          >
            <ArrowsPointingInIcon className="h-4 w-4 mr-1" />
            合并
          </button>

          <button
            onClick={clearAllFiles}
            className="btn-secondary text-sm"
          >
            <TrashIcon className="h-4 w-4 mr-1" />
            清空全部
          </button>

          <button className="btn-primary text-sm">
            <ArrowDownTrayIcon className="h-4 w-4 mr-1" />
            批量下载
          </button>
        </div>
      </div>
      
      <div className="space-y-3">
        {files.map((file) => (
          <FileItem
            key={file.id}
            file={file}
            onRemove={() => removeFile(file.id)}
          />
        ))}
      </div>

      {showMerger && (
        <FileMerger
          files={files}
          onMergeComplete={handleMergeComplete}
          onClose={() => setShowMerger(false)}
        />
      )}
    </div>
  )
}

export default FileList
