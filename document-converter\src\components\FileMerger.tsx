import { useState } from 'react'
import { 
  DocumentIcon, 
  ArrowsPointingInIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  TrashIcon
} from '@heroicons/react/24/outline'
import { FileData } from '../types/file'
import { ConversionService } from '../services/conversionService'

interface FileMergerProps {
  files: FileData[]
  onMergeComplete: (mergedFile: File) => void
  onClose: () => void
}

const FileMerger = ({ files, onMergeComplete, onClose }: FileMergerProps) => {
  const [selectedFiles, setSelectedFiles] = useState<FileData[]>(
    files.filter(f => f.type === 'application/pdf')
  )
  const [isProcessing, setIsProcessing] = useState(false)
  const [mergeType, setMergeType] = useState<'pdf' | 'images'>('pdf')

  const pdfFiles = files.filter(f => f.type === 'application/pdf')
  const imageFiles = files.filter(f => f.type.startsWith('image/'))

  const handleMerge = async () => {
    if (selectedFiles.length < 2) {
      alert('请选择至少2个文件进行合并')
      return
    }

    setIsProcessing(true)
    try {
      let mergedFile: File

      if (mergeType === 'pdf') {
        mergedFile = await ConversionService.mergePdfs(selectedFiles.map(f => f.file))
      } else {
        // 图片合并为PDF
        mergedFile = await ConversionService.imagesToPdf(selectedFiles.map(f => f.file))
      }

      onMergeComplete(mergedFile)
    } catch (error) {
      console.error('文件合并失败:', error)
      alert('文件合并失败，请重试')
    } finally {
      setIsProcessing(false)
    }
  }

  const moveFileUp = (index: number) => {
    if (index > 0) {
      const newFiles = [...selectedFiles]
      ;[newFiles[index - 1], newFiles[index]] = [newFiles[index], newFiles[index - 1]]
      setSelectedFiles(newFiles)
    }
  }

  const moveFileDown = (index: number) => {
    if (index < selectedFiles.length - 1) {
      const newFiles = [...selectedFiles]
      ;[newFiles[index], newFiles[index + 1]] = [newFiles[index + 1], newFiles[index]]
      setSelectedFiles(newFiles)
    }
  }

  const removeFile = (index: number) => {
    setSelectedFiles(selectedFiles.filter((_, i) => i !== index))
  }

  const addFile = (file: FileData) => {
    if (!selectedFiles.find(f => f.id === file.id)) {
      setSelectedFiles([...selectedFiles, file])
    }
  }

  const availableFiles = mergeType === 'pdf' ? pdfFiles : imageFiles
  const unselectedFiles = availableFiles.filter(
    f => !selectedFiles.find(sf => sf.id === f.id)
  )

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900 flex items-center">
              <ArrowsPointingInIcon className="h-6 w-6 mr-2" />
              文件合并
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>

          {/* 合并类型选择 */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              合并类型
            </label>
            <div className="flex space-x-4">
              <label className="flex items-center">
                <input
                  type="radio"
                  value="pdf"
                  checked={mergeType === 'pdf'}
                  onChange={(e) => {
                    setMergeType(e.target.value as 'pdf')
                    setSelectedFiles(pdfFiles.slice(0, 2))
                  }}
                  className="mr-2"
                />
                PDF合并
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  value="images"
                  checked={mergeType === 'images'}
                  onChange={(e) => {
                    setMergeType(e.target.value as 'images')
                    setSelectedFiles(imageFiles.slice(0, 2))
                  }}
                  className="mr-2"
                />
                图片合并为PDF
              </label>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 可选文件列表 */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                可选文件 ({unselectedFiles.length})
              </h3>
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {unselectedFiles.map((file) => (
                  <div
                    key={file.id}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                  >
                    <div className="flex items-center space-x-3">
                      <DocumentIcon className="h-5 w-5 text-gray-500" />
                      <div>
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {file.name}
                        </p>
                        <p className="text-xs text-gray-500">
                          {(file.size / 1024 / 1024).toFixed(2)} MB
                        </p>
                      </div>
                    </div>
                    <button
                      onClick={() => addFile(file)}
                      className="btn-secondary text-xs px-3 py-1"
                    >
                      添加
                    </button>
                  </div>
                ))}
                {unselectedFiles.length === 0 && (
                  <p className="text-gray-500 text-center py-4">
                    没有可用的{mergeType === 'pdf' ? 'PDF' : '图片'}文件
                  </p>
                )}
              </div>
            </div>

            {/* 已选文件列表 */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                合并顺序 ({selectedFiles.length})
              </h3>
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {selectedFiles.map((file, index) => (
                  <div
                    key={file.id}
                    className="flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-200"
                  >
                    <div className="flex items-center space-x-3">
                      <span className="text-sm font-medium text-blue-600 bg-blue-100 rounded-full w-6 h-6 flex items-center justify-center">
                        {index + 1}
                      </span>
                      <DocumentIcon className="h-5 w-5 text-blue-500" />
                      <div>
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {file.name}
                        </p>
                        <p className="text-xs text-gray-500">
                          {(file.size / 1024 / 1024).toFixed(2)} MB
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-1">
                      <button
                        onClick={() => moveFileUp(index)}
                        disabled={index === 0}
                        className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50"
                      >
                        <ArrowUpIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => moveFileDown(index)}
                        disabled={index === selectedFiles.length - 1}
                        className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50"
                      >
                        <ArrowDownIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => removeFile(index)}
                        className="p-1 text-gray-400 hover:text-red-500"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                ))}
                {selectedFiles.length === 0 && (
                  <p className="text-gray-500 text-center py-4">
                    请从左侧添加文件
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* 合并按钮 */}
          <div className="mt-6 flex justify-end space-x-3">
            <button
              onClick={onClose}
              className="btn-secondary"
            >
              取消
            </button>
            <button
              onClick={handleMerge}
              disabled={selectedFiles.length < 2 || isProcessing}
              className="btn-primary"
            >
              {isProcessing ? '合并中...' : `合并 ${selectedFiles.length} 个文件`}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default FileMerger
