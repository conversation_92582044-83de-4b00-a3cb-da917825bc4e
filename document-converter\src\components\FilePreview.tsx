import { useState, useEffect } from 'react'
import { 
  EyeIcon, 
  DocumentIcon, 
  PhotoIcon,
  XMarkIcon,
  ArrowsPointingOutIcon,
  ArrowsPointingInIcon
} from '@heroicons/react/24/outline'
import type { FileData } from '../types/file'

interface FilePreviewProps {
  file: FileData
  convertedFile?: File
  onClose: () => void
}

const FilePreview = ({ file, convertedFile, onClose }: FilePreviewProps) => {
  const [activeTab, setActiveTab] = useState<'original' | 'converted'>('original')
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [originalPreview, setOriginalPreview] = useState<string | null>(null)
  const [convertedPreview, setConvertedPreview] = useState<string | null>(null)

  useEffect(() => {
    // 生成原文件预览
    generatePreview(file.file, setOriginalPreview)
    
    // 生成转换后文件预览
    if (convertedFile) {
      generatePreview(convertedFile, setConvertedPreview)
    }

    return () => {
      // 清理URL对象
      if (originalPreview) URL.revokeObjectURL(originalPreview)
      if (convertedPreview) URL.revokeObjectURL(convertedPreview)
    }
  }, [file, convertedFile])

  const generatePreview = async (fileToPreview: File, setPreview: (url: string | null) => void) => {
    try {
      if (fileToPreview.type.startsWith('image/')) {
        // 图片文件直接显示
        const url = URL.createObjectURL(fileToPreview)
        setPreview(url)
      } else if (fileToPreview.type === 'application/pdf') {
        // PDF文件显示第一页缩略图
        const url = URL.createObjectURL(fileToPreview)
        setPreview(url)
      } else if (fileToPreview.type.startsWith('text/')) {
        // 文本文件显示内容
        const text = await fileToPreview.text()
        const blob = new Blob([text], { type: 'text/plain' })
        const url = URL.createObjectURL(blob)
        setPreview(url)
      } else {
        // 其他文件类型显示文件信息
        setPreview(null)
      }
    } catch (error) {
      console.error('生成预览失败:', error)
      setPreview(null)
    }
  }

  const renderPreview = (preview: string | null, fileToRender: File) => {
    if (!preview) {
      return (
        <div className="flex flex-col items-center justify-center h-64 bg-gray-100 rounded-lg">
          <DocumentIcon className="h-16 w-16 text-gray-400 mb-4" />
          <p className="text-gray-600 text-center">
            无法预览此文件类型
          </p>
          <p className="text-sm text-gray-500 mt-2">
            {fileToRender.name}
          </p>
          <p className="text-sm text-gray-500">
            {(fileToRender.size / 1024 / 1024).toFixed(2)} MB
          </p>
        </div>
      )
    }

    if (fileToRender.type.startsWith('image/')) {
      return (
        <div className="flex justify-center">
          <img
            src={preview}
            alt={fileToRender.name}
            className="max-w-full max-h-96 object-contain rounded-lg shadow-sm"
          />
        </div>
      )
    }

    if (fileToRender.type === 'application/pdf') {
      return (
        <div className="flex justify-center">
          <iframe
            src={preview}
            className="w-full h-96 border border-gray-300 rounded-lg"
            title={`PDF预览: ${fileToRender.name}`}
          />
        </div>
      )
    }

    if (fileToRender.type.startsWith('text/')) {
      return (
        <div className="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
          <iframe
            src={preview}
            className="w-full h-80 border-none"
            title={`文本预览: ${fileToRender.name}`}
          />
        </div>
      )
    }

    return (
      <div className="flex flex-col items-center justify-center h-64 bg-gray-100 rounded-lg">
        <DocumentIcon className="h-16 w-16 text-gray-400 mb-4" />
        <p className="text-gray-600">预览不可用</p>
      </div>
    )
  }

  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith('image/')) {
      return <PhotoIcon className="h-5 w-5" />
    }
    return <DocumentIcon className="h-5 w-5" />
  }

  return (
    <div className={`fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 ${
      isFullscreen ? 'p-0' : 'p-4'
    }`}>
      <div className={`bg-white rounded-xl shadow-xl ${
        isFullscreen ? 'w-full h-full rounded-none' : 'max-w-6xl w-full max-h-[90vh]'
      } overflow-hidden`}>
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900 flex items-center">
            <EyeIcon className="h-6 w-6 mr-2" />
            文件预览
          </h2>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setIsFullscreen(!isFullscreen)}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              title={isFullscreen ? '退出全屏' : '全屏显示'}
            >
              {isFullscreen ? (
                <ArrowsPointingInIcon className="h-5 w-5" />
              ) : (
                <ArrowsPointingOutIcon className="h-5 w-5" />
              )}
            </button>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <XMarkIcon className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* 标签页 */}
        <div className="flex border-b border-gray-200">
          <button
            onClick={() => setActiveTab('original')}
            className={`flex items-center space-x-2 px-6 py-3 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'original'
                ? 'border-primary-500 text-primary-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            {getFileIcon(file.type)}
            <span>原文件</span>
            <span className="text-xs text-gray-400">({file.name})</span>
          </button>
          
          {convertedFile && (
            <button
              onClick={() => setActiveTab('converted')}
              className={`flex items-center space-x-2 px-6 py-3 text-sm font-medium border-b-2 transition-colors ${
                activeTab === 'converted'
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              {getFileIcon(convertedFile.type)}
              <span>转换后</span>
              <span className="text-xs text-gray-400">({convertedFile.name})</span>
            </button>
          )}
        </div>

        {/* 预览内容 */}
        <div className={`p-6 ${isFullscreen ? 'h-full overflow-y-auto' : 'max-h-96 overflow-y-auto'}`}>
          {activeTab === 'original' && renderPreview(originalPreview, file.file)}
          {activeTab === 'converted' && convertedFile && renderPreview(convertedPreview, convertedFile)}
        </div>

        {/* 文件信息 */}
        <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">原文件信息</h4>
              <div className="space-y-1 text-gray-600">
                <p>文件名: {file.name}</p>
                <p>大小: {(file.size / 1024 / 1024).toFixed(2)} MB</p>
                <p>类型: {file.type}</p>
              </div>
            </div>
            
            {convertedFile && (
              <div>
                <h4 className="font-medium text-gray-900 mb-2">转换后文件信息</h4>
                <div className="space-y-1 text-gray-600">
                  <p>文件名: {convertedFile.name}</p>
                  <p>大小: {(convertedFile.size / 1024 / 1024).toFixed(2)} MB</p>
                  <p>类型: {convertedFile.type}</p>
                  <p className="text-green-600">
                    压缩率: {((1 - convertedFile.size / file.size) * 100).toFixed(1)}%
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default FilePreview
