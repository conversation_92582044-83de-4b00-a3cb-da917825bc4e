import { useState } from 'react'
import { 
  PhotoIcon, 
  ArrowPathIcon, 
  ScissorsIcon,
  AdjustmentsHorizontalIcon,
  SparklesIcon
} from '@heroicons/react/24/outline'
import { FileData } from '../types/file'
import { ImageService } from '../services/imageService'

interface ImageEditorProps {
  file: FileData
  onSave: (editedFile: File) => void
  onClose: () => void
}

const ImageEditor = ({ file, onSave, onClose }: ImageEditorProps) => {
  const [activeTab, setActiveTab] = useState<'resize' | 'rotate' | 'compress' | 'watermark'>('resize')
  const [isProcessing, setIsProcessing] = useState(false)
  
  // 缩放选项
  const [resizeWidth, setResizeWidth] = useState<number>(800)
  const [resizeHeight, setResizeHeight] = useState<number>(600)
  const [maintainAspectRatio, setMaintainAspectRatio] = useState(true)
  
  // 旋转选项
  const [rotationDegrees, setRotationDegrees] = useState<number>(90)
  
  // 压缩选项
  const [compressionQuality, setCompressionQuality] = useState<number>(0.8)
  const [maxWidth, setMaxWidth] = useState<number>(1920)
  const [maxHeight, setMaxHeight] = useState<number>(1080)
  
  // 水印选项
  const [watermarkText, setWatermarkText] = useState<string>('Watermark')
  const [watermarkPosition, setWatermarkPosition] = useState<'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'center'>('bottom-right')
  const [watermarkOpacity, setWatermarkOpacity] = useState<number>(0.7)

  const handleResize = async () => {
    setIsProcessing(true)
    try {
      const resizedFile = await ImageService.resizeImage(
        file.file, 
        resizeWidth, 
        resizeHeight, 
        maintainAspectRatio
      )
      onSave(resizedFile)
    } catch (error) {
      console.error('图片缩放失败:', error)
    } finally {
      setIsProcessing(false)
    }
  }

  const handleRotate = async () => {
    setIsProcessing(true)
    try {
      const rotatedFile = await ImageService.rotateImage(file.file, rotationDegrees)
      onSave(rotatedFile)
    } catch (error) {
      console.error('图片旋转失败:', error)
    } finally {
      setIsProcessing(false)
    }
  }

  const handleCompress = async () => {
    setIsProcessing(true)
    try {
      const compressedFile = await ImageService.compressImage(
        file.file, 
        compressionQuality, 
        maxWidth, 
        maxHeight
      )
      onSave(compressedFile)
    } catch (error) {
      console.error('图片压缩失败:', error)
    } finally {
      setIsProcessing(false)
    }
  }

  const handleAddWatermark = async () => {
    setIsProcessing(true)
    try {
      const watermarkedFile = await ImageService.addWatermark(file.file, watermarkText, {
        position: watermarkPosition,
        opacity: watermarkOpacity
      })
      onSave(watermarkedFile)
    } catch (error) {
      console.error('添加水印失败:', error)
    } finally {
      setIsProcessing(false)
    }
  }

  const tabs = [
    { id: 'resize', name: '缩放', icon: ScissorsIcon },
    { id: 'rotate', name: '旋转', icon: ArrowPathIcon },
    { id: 'compress', name: '压缩', icon: AdjustmentsHorizontalIcon },
    { id: 'watermark', name: '水印', icon: SparklesIcon },
  ]

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900 flex items-center">
              <PhotoIcon className="h-6 w-6 mr-2" />
              图片编辑 - {file.name}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>

          {/* 标签页 */}
          <div className="flex space-x-1 mb-6 bg-gray-100 rounded-lg p-1">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'bg-white text-primary-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <tab.icon className="h-4 w-4" />
                <span>{tab.name}</span>
              </button>
            ))}
          </div>

          {/* 缩放选项 */}
          {activeTab === 'resize' && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    宽度 (px)
                  </label>
                  <input
                    type="number"
                    value={resizeWidth}
                    onChange={(e) => setResizeWidth(Number(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    高度 (px)
                  </label>
                  <input
                    type="number"
                    value={resizeHeight}
                    onChange={(e) => setResizeHeight(Number(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  />
                </div>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="aspectRatio"
                  checked={maintainAspectRatio}
                  onChange={(e) => setMaintainAspectRatio(e.target.checked)}
                  className="mr-2"
                />
                <label htmlFor="aspectRatio" className="text-sm text-gray-700">
                  保持宽高比
                </label>
              </div>

              <button
                onClick={handleResize}
                disabled={isProcessing}
                className="btn-primary w-full"
              >
                {isProcessing ? '处理中...' : '应用缩放'}
              </button>
            </div>
          )}

          {/* 旋转选项 */}
          {activeTab === 'rotate' && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  旋转角度
                </label>
                <select
                  value={rotationDegrees}
                  onChange={(e) => setRotationDegrees(Number(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                >
                  <option value={90}>90° (顺时针)</option>
                  <option value={180}>180°</option>
                  <option value={270}>270° (逆时针)</option>
                  <option value={-90}>-90° (逆时针)</option>
                </select>
              </div>

              <button
                onClick={handleRotate}
                disabled={isProcessing}
                className="btn-primary w-full"
              >
                {isProcessing ? '处理中...' : '应用旋转'}
              </button>
            </div>
          )}

          {/* 压缩选项 */}
          {activeTab === 'compress' && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  压缩质量: {Math.round(compressionQuality * 100)}%
                </label>
                <input
                  type="range"
                  min="0.1"
                  max="1"
                  step="0.1"
                  value={compressionQuality}
                  onChange={(e) => setCompressionQuality(Number(e.target.value))}
                  className="w-full"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    最大宽度 (px)
                  </label>
                  <input
                    type="number"
                    value={maxWidth}
                    onChange={(e) => setMaxWidth(Number(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    最大高度 (px)
                  </label>
                  <input
                    type="number"
                    value={maxHeight}
                    onChange={(e) => setMaxHeight(Number(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  />
                </div>
              </div>

              <button
                onClick={handleCompress}
                disabled={isProcessing}
                className="btn-primary w-full"
              >
                {isProcessing ? '处理中...' : '应用压缩'}
              </button>
            </div>
          )}

          {/* 水印选项 */}
          {activeTab === 'watermark' && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  水印文字
                </label>
                <input
                  type="text"
                  value={watermarkText}
                  onChange={(e) => setWatermarkText(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  水印位置
                </label>
                <select
                  value={watermarkPosition}
                  onChange={(e) => setWatermarkPosition(e.target.value as any)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                >
                  <option value="top-left">左上角</option>
                  <option value="top-right">右上角</option>
                  <option value="bottom-left">左下角</option>
                  <option value="bottom-right">右下角</option>
                  <option value="center">居中</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  透明度: {Math.round(watermarkOpacity * 100)}%
                </label>
                <input
                  type="range"
                  min="0.1"
                  max="1"
                  step="0.1"
                  value={watermarkOpacity}
                  onChange={(e) => setWatermarkOpacity(Number(e.target.value))}
                  className="w-full"
                />
              </div>

              <button
                onClick={handleAddWatermark}
                disabled={isProcessing}
                className="btn-primary w-full"
              >
                {isProcessing ? '处理中...' : '添加水印'}
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default ImageEditor
