import { useState, useEffect } from 'react'
import { CheckCircleIcon, ExclamationCircleIcon } from '@heroicons/react/24/outline'

interface ProgressStep {
  id: string
  label: string
  status: 'pending' | 'processing' | 'completed' | 'error'
  progress?: number
  error?: string
}

interface ProgressIndicatorProps {
  steps: ProgressStep[]
  title?: string
  showDetails?: boolean
  onCancel?: () => void
}

const ProgressIndicator = ({ 
  steps, 
  title = '处理进度', 
  showDetails = true,
  onCancel 
}: ProgressIndicatorProps) => {
  const [currentStepIndex, setCurrentStepIndex] = useState(0)

  useEffect(() => {
    const processingIndex = steps.findIndex(step => step.status === 'processing')
    if (processingIndex !== -1) {
      setCurrentStepIndex(processingIndex)
    }
  }, [steps])

  const getOverallProgress = () => {
    const completedSteps = steps.filter(step => step.status === 'completed').length
    const processingStep = steps.find(step => step.status === 'processing')
    
    let progress = (completedSteps / steps.length) * 100
    
    if (processingStep && processingStep.progress) {
      progress += (processingStep.progress / 100) * (1 / steps.length) * 100
    }
    
    return Math.min(progress, 100)
  }

  const getStepIcon = (step: ProgressStep) => {
    switch (step.status) {
      case 'completed':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />
      case 'error':
        return <ExclamationCircleIcon className="h-5 w-5 text-red-500" />
      case 'processing':
        return (
          <div className="h-5 w-5 border-2 border-primary-500 border-t-transparent rounded-full animate-spin" />
        )
      default:
        return (
          <div className="h-5 w-5 border-2 border-gray-300 rounded-full" />
        )
    }
  }

  const hasError = steps.some(step => step.status === 'error')
  const isCompleted = steps.every(step => step.status === 'completed')
  const isProcessing = steps.some(step => step.status === 'processing')

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900">{title}</h3>
        {onCancel && isProcessing && (
          <button
            onClick={onCancel}
            className="text-sm text-gray-500 hover:text-red-500 transition-colors"
          >
            取消
          </button>
        )}
      </div>

      {/* 总体进度条 */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700">
            总体进度
          </span>
          <span className="text-sm text-gray-600">
            {Math.round(getOverallProgress())}%
          </span>
        </div>
        <div className="progress-bar">
          <div 
            className={`progress-fill transition-all duration-500 ${
              hasError ? 'bg-red-500' : 
              isCompleted ? 'bg-green-500' : 
              'bg-primary-500'
            }`}
            style={{ width: `${getOverallProgress()}%` }}
          />
        </div>
      </div>

      {/* 步骤详情 */}
      {showDetails && (
        <div className="space-y-3">
          {steps.map((step, index) => (
            <div
              key={step.id}
              className={`flex items-center space-x-3 p-3 rounded-lg transition-colors ${
                step.status === 'processing' ? 'bg-blue-50' :
                step.status === 'completed' ? 'bg-green-50' :
                step.status === 'error' ? 'bg-red-50' :
                'bg-gray-50'
              }`}
            >
              <div className="flex-shrink-0">
                {getStepIcon(step)}
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <p className={`text-sm font-medium ${
                    step.status === 'error' ? 'text-red-900' :
                    step.status === 'completed' ? 'text-green-900' :
                    step.status === 'processing' ? 'text-blue-900' :
                    'text-gray-900'
                  }`}>
                    {step.label}
                  </p>
                  
                  {step.status === 'processing' && step.progress !== undefined && (
                    <span className="text-sm text-gray-600">
                      {step.progress}%
                    </span>
                  )}
                </div>
                
                {step.status === 'processing' && step.progress !== undefined && (
                  <div className="mt-2 progress-bar h-1">
                    <div 
                      className="progress-fill h-1"
                      style={{ width: `${step.progress}%` }}
                    />
                  </div>
                )}
                
                {step.error && (
                  <p className="mt-1 text-sm text-red-600">
                    {step.error}
                  </p>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* 状态摘要 */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center space-x-4">
            <span className="text-gray-600">
              已完成: {steps.filter(s => s.status === 'completed').length}
            </span>
            <span className="text-gray-600">
              总计: {steps.length}
            </span>
          </div>
          
          <div className={`font-medium ${
            hasError ? 'text-red-600' :
            isCompleted ? 'text-green-600' :
            isProcessing ? 'text-blue-600' :
            'text-gray-600'
          }`}>
            {hasError ? '处理失败' :
             isCompleted ? '全部完成' :
             isProcessing ? '处理中...' :
             '等待开始'}
          </div>
        </div>
      </div>
    </div>
  )
}

export default ProgressIndicator
