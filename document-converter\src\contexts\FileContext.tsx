import React, { createContext, useContext, useState, useCallback, useEffect } from 'react'
import { FileData, ConversionStatus, ConversionOptions, FileContextType } from '../types/file'
import { workerManager } from '../services/workerManager'
import { useNotification } from './NotificationContext'
import { HistoryService } from '../services/historyService'

const FileContext = createContext<FileContextType | undefined>(undefined)

export const useFileContext = () => {
  const context = useContext(FileContext)
  if (!context) {
    throw new Error('useFileContext must be used within a FileProvider')
  }
  return context
}

interface FileProviderProps {
  children: React.ReactNode
}

export const FileProvider: React.FC<FileProviderProps> = ({ children }) => {
  const [files, setFiles] = useState<FileData[]>([])
  const { showSuccess, showError, showInfo } = useNotification()

  // 清理URL对象
  useEffect(() => {
    return () => {
      files.forEach(file => {
        if (file.convertedUrl) {
          URL.revokeObjectURL(file.convertedUrl)
        }
      })
      workerManager.destroy()
    }
  }, [files])

  const addFiles = useCallback((newFiles: File[]) => {
    const fileDataArray: FileData[] = newFiles.map(file => ({
      id: `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      name: file.name,
      size: file.size,
      type: file.type,
      file,
      status: 'pending' as ConversionStatus,
    }))

    setFiles(prev => [...prev, ...fileDataArray])

    if (newFiles.length === 1) {
      showInfo('文件已添加', `${newFiles[0].name} 已添加到转换列表`)
    } else {
      showInfo('文件已添加', `已添加 ${newFiles.length} 个文件到转换列表`)
    }
  }, [showInfo])

  const removeFile = useCallback((id: string) => {
    setFiles(prev => prev.filter(file => file.id !== id))
  }, [])

  const clearAllFiles = useCallback(() => {
    setFiles([])
  }, [])

  const updateFileStatus = useCallback((id: string, status: ConversionStatus, progress?: number) => {
    setFiles(prev => prev.map(file => 
      file.id === id 
        ? { ...file, status, progress }
        : file
    ))
  }, [])

  const convertFile = useCallback(async (id: string, options: ConversionOptions) => {
    const fileData = files.find(f => f.id === id)
    if (!fileData) return

    const startTime = Date.now()
    updateFileStatus(id, 'processing', 0)
    showInfo('开始转换', `正在转换 ${fileData.name}`)

    try {
      const result = await workerManager.convertFile(
        fileData.file,
        options,
        (progress) => updateFileStatus(id, 'processing', progress)
      )

      const endTime = Date.now()
      const processingTime = endTime - startTime
      const convertedFile = Array.isArray(result) ? result[0] : result

      // 更新文件数据
      setFiles(prev => prev.map(file =>
        file.id === id
          ? {
              ...file,
              status: 'completed' as ConversionStatus,
              progress: 100,
              convertedFile,
              convertedUrl: URL.createObjectURL(convertedFile)
            }
          : file
      ))

      // 添加到历史记录
      HistoryService.addHistoryItem({
        fileName: fileData.name,
        originalSize: fileData.size,
        convertedSize: convertedFile.size,
        fromFormat: fileData.type.split('/')[1] || fileData.type,
        toFormat: options.format,
        status: 'completed',
        quality: options.quality || 'standard',
        processingTime
      })

      showSuccess('转换完成', `${fileData.name} 已成功转换为 ${options.format.toUpperCase()}`)
    } catch (error) {
      const endTime = Date.now()
      const processingTime = endTime - startTime

      console.error('Conversion error:', error)
      updateFileStatus(id, 'error')

      // 添加失败记录到历史
      HistoryService.addHistoryItem({
        fileName: fileData.name,
        originalSize: fileData.size,
        convertedSize: 0,
        fromFormat: fileData.type.split('/')[1] || fileData.type,
        toFormat: options.format,
        status: 'failed',
        quality: options.quality || 'standard',
        processingTime,
        error: error instanceof Error ? error.message : '未知错误'
      })

      showError('转换失败', `${fileData.name} 转换失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }, [files, updateFileStatus, showInfo, showSuccess, showError])

  const value: FileContextType = {
    files,
    addFiles,
    removeFile,
    clearAllFiles,
    updateFileStatus,
    convertFile,
  }

  return (
    <FileContext.Provider value={value}>
      {children}
    </FileContext.Provider>
  )
}
