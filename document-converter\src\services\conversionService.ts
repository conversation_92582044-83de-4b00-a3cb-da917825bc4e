import { PDFDocument, rgb } from 'pdf-lib'
import J<PERSON><PERSON><PERSON> from 'jszip'
import { saveAs } from 'file-saver'
import { ConversionOptions } from '../types/file'
import { PDFService } from './pdfService'
import { OfficeService } from './officeService'
import { ImageService } from './imageService'

export class ConversionService {
  // PDF相关转换
  static async pdfToImages(file: File, options: ConversionOptions): Promise<File[]> {
    try {
      const arrayBuffer = await file.arrayBuffer()
      const pdfDoc = await PDFDocument.load(arrayBuffer)
      const pages = pdfDoc.getPages()
      const images: File[] = []

      for (let i = 0; i < pages.length; i++) {
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')
        if (!ctx) continue

        // 这里需要使用PDF.js或其他库来渲染PDF页面到canvas
        // 暂时创建一个占位符图片
        canvas.width = 800
        canvas.height = 1000
        ctx.fillStyle = '#ffffff'
        ctx.fillRect(0, 0, canvas.width, canvas.height)
        ctx.fillStyle = '#000000'
        ctx.font = '20px Arial'
        ctx.fillText(`PDF页面 ${i + 1}`, 50, 50)

        const blob = await new Promise<Blob>((resolve) => {
          canvas.toBlob((blob) => resolve(blob!), `image/${options.format}`)
        })

        const imageFile = new File([blob], `${file.name.replace('.pdf', '')}_page_${i + 1}.${options.format}`, {
          type: `image/${options.format}`
        })
        images.push(imageFile)
      }

      return images
    } catch (error) {
      console.error('PDF转图片失败:', error)
      throw new Error('PDF转换失败')
    }
  }

  // 图片转PDF
  static async imagesToPdf(files: File[]): Promise<File> {
    try {
      const pdfDoc = await PDFDocument.create()

      for (const file of files) {
        const arrayBuffer = await file.arrayBuffer()
        let image

        if (file.type === 'image/jpeg' || file.type === 'image/jpg') {
          image = await pdfDoc.embedJpg(arrayBuffer)
        } else if (file.type === 'image/png') {
          image = await pdfDoc.embedPng(arrayBuffer)
        } else {
          // 对于其他格式，先转换为canvas再转为PNG
          const canvas = await this.imageToCanvas(file)
          const pngBlob = await new Promise<Blob>((resolve) => {
            canvas.toBlob((blob) => resolve(blob!), 'image/png')
          })
          const pngBuffer = await pngBlob.arrayBuffer()
          image = await pdfDoc.embedPng(pngBuffer)
        }

        const page = pdfDoc.addPage([image.width, image.height])
        page.drawImage(image, {
          x: 0,
          y: 0,
          width: image.width,
          height: image.height,
        })
      }

      const pdfBytes = await pdfDoc.save()
      return new File([pdfBytes], 'converted.pdf', { type: 'application/pdf' })
    } catch (error) {
      console.error('图片转PDF失败:', error)
      throw new Error('图片转PDF失败')
    }
  }

  // 图片格式转换
  static async convertImageFormat(file: File, targetFormat: string, quality: number = 0.9): Promise<File> {
    return ImageService.convertFormat(file, targetFormat, quality)
  }

  // 辅助方法：将图片文件转换为Canvas
  private static async imageToCanvas(file: File): Promise<HTMLCanvasElement> {
    return new Promise((resolve, reject) => {
      const img = new Image()
      img.onload = () => {
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')
        if (!ctx) {
          reject(new Error('无法获取canvas上下文'))
          return
        }

        canvas.width = img.width
        canvas.height = img.height
        ctx.drawImage(img, 0, 0)
        resolve(canvas)
      }
      img.onerror = () => reject(new Error('图片加载失败'))
      img.src = URL.createObjectURL(file)
    })
  }

  // PDF合并
  static async mergePdfs(files: File[]): Promise<File> {
    try {
      const mergedPdf = await PDFDocument.create()

      for (const file of files) {
        const arrayBuffer = await file.arrayBuffer()
        const pdf = await PDFDocument.load(arrayBuffer)
        const copiedPages = await mergedPdf.copyPages(pdf, pdf.getPageIndices())
        copiedPages.forEach((page) => mergedPdf.addPage(page))
      }

      const pdfBytes = await mergedPdf.save()
      return new File([pdfBytes], 'merged.pdf', { type: 'application/pdf' })
    } catch (error) {
      console.error('PDF合并失败:', error)
      throw new Error('PDF合并失败')
    }
  }

  // 文件压缩
  static async compressFile(file: File, quality: number = 0.8): Promise<File> {
    if (file.type.startsWith('image/')) {
      return ImageService.compressImage(file, quality)
    }

    if (file.type === 'application/pdf') {
      return PDFService.compressPdf(file)
    }

    // 对于其他文件类型，暂时返回原文件
    return file
  }

  // 批量下载（打包为ZIP）
  static async downloadAsZip(files: File[], zipName: string = 'converted_files.zip'): Promise<void> {
    try {
      const zip = new JSZip()
      
      files.forEach((file, index) => {
        zip.file(`${index + 1}_${file.name}`, file)
      })

      const content = await zip.generateAsync({ type: 'blob' })
      saveAs(content, zipName)
    } catch (error) {
      console.error('打包下载失败:', error)
      throw new Error('打包下载失败')
    }
  }

  // 单文件下载
  static downloadFile(file: File): void {
    saveAs(file, file.name)
  }

  // 主要转换方法
  static async convertFile(file: File, options: ConversionOptions): Promise<File | File[]> {
    const { format, quality = 'standard' } = options
    const qualityValue = quality === 'high' ? 0.95 : quality === 'standard' ? 0.8 : 0.6

    // PDF转换
    if (file.type === 'application/pdf') {
      if (format === 'jpg' || format === 'png') {
        return PDFService.pdfToImages(file, format)
      }
    }

    // Word文档转换
    if (file.type.includes('word') || file.type.includes('document') || file.name.match(/\.(doc|docx)$/i)) {
      if (format === 'pdf') {
        return OfficeService.wordToPdf(file)
      } else if (format === 'txt') {
        const text = await OfficeService.wordToText(file)
        return new File([text], file.name.replace(/\.(doc|docx)$/i, '.txt'), { type: 'text/plain' })
      }
    }

    // Excel文档转换
    if (file.type.includes('spreadsheet') || file.name.match(/\.(xls|xlsx)$/i)) {
      if (format === 'pdf') {
        return OfficeService.excelToPdf(file)
      } else if (format === 'csv') {
        return OfficeService.excelToCsv(file)
      }
    }

    // PowerPoint文档转换
    if (file.type.includes('presentation') || file.name.match(/\.(ppt|pptx)$/i)) {
      if (format === 'pdf') {
        return OfficeService.powerPointToPdf(file)
      }
    }

    // 图片转换
    if (file.type.startsWith('image/')) {
      if (format === 'pdf') {
        return this.imagesToPdf([file])
      } else if (format !== file.type.split('/')[1]) {
        return this.convertImageFormat(file, format, qualityValue)
      }
    }

    // 如果没有匹配的转换，返回原文件
    return file
  }
}
