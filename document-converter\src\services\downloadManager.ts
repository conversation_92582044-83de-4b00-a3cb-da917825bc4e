import J<PERSON>Z<PERSON> from 'jszip'
import { saveAs } from 'file-saver'

export interface DownloadItem {
  id: string
  name: string
  file: File
  status: 'pending' | 'downloading' | 'completed' | 'error'
  progress: number
  error?: string
}

export class DownloadManager {
  private static downloads: Map<string, DownloadItem> = new Map()
  private static listeners: Set<(downloads: DownloadItem[]) => void> = new Set()

  // 添加监听器
  static addListener(listener: (downloads: DownloadItem[]) => void): () => void {
    this.listeners.add(listener)
    return () => this.listeners.delete(listener)
  }

  // 通知监听器
  private static notifyListeners(): void {
    const downloads = Array.from(this.downloads.values())
    this.listeners.forEach(listener => listener(downloads))
  }

  // 单文件下载
  static async downloadFile(file: File, customName?: string): Promise<void> {
    const id = `download_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
    const downloadItem: DownloadItem = {
      id,
      name: customName || file.name,
      file,
      status: 'pending',
      progress: 0
    }

    this.downloads.set(id, downloadItem)
    this.notifyListeners()

    try {
      // 更新状态为下载中
      downloadItem.status = 'downloading'
      downloadItem.progress = 50
      this.notifyListeners()

      // 使用file-saver下载文件
      saveAs(file, downloadItem.name)

      // 模拟下载完成
      setTimeout(() => {
        downloadItem.status = 'completed'
        downloadItem.progress = 100
        this.notifyListeners()

        // 5秒后从列表中移除
        setTimeout(() => {
          this.downloads.delete(id)
          this.notifyListeners()
        }, 5000)
      }, 1000)

    } catch (error) {
      downloadItem.status = 'error'
      downloadItem.error = error instanceof Error ? error.message : '下载失败'
      this.notifyListeners()
    }
  }

  // 批量下载（打包为ZIP）
  static async downloadMultipleFiles(
    files: { file: File; name?: string }[],
    zipName: string = 'converted_files.zip'
  ): Promise<void> {
    const id = `batch_download_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
    const downloadItem: DownloadItem = {
      id,
      name: zipName,
      file: new File([], zipName), // 占位符
      status: 'pending',
      progress: 0
    }

    this.downloads.set(id, downloadItem)
    this.notifyListeners()

    try {
      downloadItem.status = 'downloading'
      this.notifyListeners()

      const zip = new JSZip()
      
      // 添加文件到ZIP
      files.forEach((item, index) => {
        const fileName = item.name || item.file.name
        zip.file(fileName, item.file)
        
        // 更新进度
        downloadItem.progress = Math.round(((index + 1) / files.length) * 50)
        this.notifyListeners()
      })

      // 生成ZIP文件
      downloadItem.progress = 75
      this.notifyListeners()

      const content = await zip.generateAsync({ 
        type: 'blob',
        compression: 'DEFLATE',
        compressionOptions: { level: 6 }
      })

      downloadItem.progress = 90
      this.notifyListeners()

      // 下载ZIP文件
      saveAs(content, zipName)

      downloadItem.status = 'completed'
      downloadItem.progress = 100
      this.notifyListeners()

      // 5秒后从列表中移除
      setTimeout(() => {
        this.downloads.delete(id)
        this.notifyListeners()
      }, 5000)

    } catch (error) {
      downloadItem.status = 'error'
      downloadItem.error = error instanceof Error ? error.message : '打包下载失败'
      this.notifyListeners()
    }
  }

  // 下载历史记录
  static async downloadHistory(historyData: string, fileName: string = 'conversion_history.json'): Promise<void> {
    const blob = new Blob([historyData], { type: 'application/json' })
    const file = new File([blob], fileName, { type: 'application/json' })
    await this.downloadFile(file)
  }

  // 取消下载
  static cancelDownload(id: string): void {
    const download = this.downloads.get(id)
    if (download && download.status === 'downloading') {
      download.status = 'error'
      download.error = '用户取消下载'
      this.notifyListeners()
    }
  }

  // 重试下载
  static async retryDownload(id: string): Promise<void> {
    const download = this.downloads.get(id)
    if (download && download.status === 'error') {
      download.status = 'pending'
      download.progress = 0
      download.error = undefined
      this.notifyListeners()

      // 重新开始下载
      await this.downloadFile(download.file, download.name)
    }
  }

  // 清除已完成的下载
  static clearCompletedDownloads(): void {
    const completedIds: string[] = []
    this.downloads.forEach((download, id) => {
      if (download.status === 'completed') {
        completedIds.push(id)
      }
    })

    completedIds.forEach(id => this.downloads.delete(id))
    this.notifyListeners()
  }

  // 清除所有下载
  static clearAllDownloads(): void {
    this.downloads.clear()
    this.notifyListeners()
  }

  // 获取当前下载列表
  static getDownloads(): DownloadItem[] {
    return Array.from(this.downloads.values())
  }

  // 获取下载统计
  static getDownloadStats(): {
    total: number
    pending: number
    downloading: number
    completed: number
    failed: number
  } {
    const downloads = this.getDownloads()
    return {
      total: downloads.length,
      pending: downloads.filter(d => d.status === 'pending').length,
      downloading: downloads.filter(d => d.status === 'downloading').length,
      completed: downloads.filter(d => d.status === 'completed').length,
      failed: downloads.filter(d => d.status === 'error').length
    }
  }

  // 检查是否有正在进行的下载
  static hasActiveDownloads(): boolean {
    return this.getDownloads().some(d => d.status === 'downloading' || d.status === 'pending')
  }

  // 创建下载链接（用于预览）
  static createDownloadUrl(file: File): string {
    return URL.createObjectURL(file)
  }

  // 清理下载链接
  static revokeDownloadUrl(url: string): void {
    URL.revokeObjectURL(url)
  }

  // 格式化文件大小
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 估算下载时间（基于文件大小）
  static estimateDownloadTime(fileSize: number, speedKbps: number = 1000): number {
    return Math.ceil(fileSize / 1024 / speedKbps) // 返回秒数
  }

  // 验证文件类型
  static isValidFileType(file: File, allowedTypes: string[]): boolean {
    return allowedTypes.some(type => {
      if (type.endsWith('/*')) {
        return file.type.startsWith(type.slice(0, -1))
      }
      return file.type === type
    })
  }

  // 生成唯一文件名（避免重复）
  static generateUniqueFileName(originalName: string, existingNames: string[]): string {
    if (!existingNames.includes(originalName)) {
      return originalName
    }

    const nameWithoutExt = originalName.replace(/\.[^/.]+$/, '')
    const extension = originalName.match(/\.[^/.]+$/)?.[0] || ''
    
    let counter = 1
    let newName = `${nameWithoutExt}_${counter}${extension}`
    
    while (existingNames.includes(newName)) {
      counter++
      newName = `${nameWithoutExt}_${counter}${extension}`
    }
    
    return newName
  }
}
