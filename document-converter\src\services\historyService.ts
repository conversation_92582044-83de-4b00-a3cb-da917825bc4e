export interface ConversionHistoryItem {
  id: string
  fileName: string
  originalSize: number
  convertedSize: number
  fromFormat: string
  toFormat: string
  timestamp: Date
  status: 'completed' | 'failed'
  quality: 'high' | 'standard' | 'fast'
  processingTime: number // 处理时间（毫秒）
  error?: string
  downloadUrl?: string
}

export class HistoryService {
  private static readonly STORAGE_KEY = 'document-converter-history'
  private static readonly MAX_HISTORY_ITEMS = 100

  // 获取所有历史记录
  static getHistory(): ConversionHistoryItem[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY)
      if (!stored) return []
      
      const items = JSON.parse(stored) as ConversionHistoryItem[]
      // 转换日期字符串为Date对象
      return items.map(item => ({
        ...item,
        timestamp: new Date(item.timestamp)
      })).sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
    } catch (error) {
      console.error('读取历史记录失败:', error)
      return []
    }
  }

  // 添加历史记录
  static addHistoryItem(item: Omit<ConversionHistoryItem, 'id' | 'timestamp'>): void {
    try {
      const history = this.getHistory()
      const newItem: ConversionHistoryItem = {
        ...item,
        id: `history_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        timestamp: new Date()
      }

      // 添加到历史记录开头
      history.unshift(newItem)

      // 限制历史记录数量
      if (history.length > this.MAX_HISTORY_ITEMS) {
        history.splice(this.MAX_HISTORY_ITEMS)
      }

      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(history))
    } catch (error) {
      console.error('保存历史记录失败:', error)
    }
  }

  // 删除单个历史记录
  static removeHistoryItem(id: string): void {
    try {
      const history = this.getHistory()
      const filteredHistory = history.filter(item => item.id !== id)
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(filteredHistory))
    } catch (error) {
      console.error('删除历史记录失败:', error)
    }
  }

  // 清空所有历史记录
  static clearHistory(): void {
    try {
      localStorage.removeItem(this.STORAGE_KEY)
    } catch (error) {
      console.error('清空历史记录失败:', error)
    }
  }

  // 按日期范围筛选历史记录
  static getHistoryByDateRange(startDate: Date, endDate: Date): ConversionHistoryItem[] {
    const history = this.getHistory()
    return history.filter(item => 
      item.timestamp >= startDate && item.timestamp <= endDate
    )
  }

  // 按文件类型筛选历史记录
  static getHistoryByFormat(fromFormat?: string, toFormat?: string): ConversionHistoryItem[] {
    const history = this.getHistory()
    return history.filter(item => {
      const matchesFrom = !fromFormat || item.fromFormat.toLowerCase().includes(fromFormat.toLowerCase())
      const matchesTo = !toFormat || item.toFormat.toLowerCase().includes(toFormat.toLowerCase())
      return matchesFrom && matchesTo
    })
  }

  // 按状态筛选历史记录
  static getHistoryByStatus(status: 'completed' | 'failed'): ConversionHistoryItem[] {
    const history = this.getHistory()
    return history.filter(item => item.status === status)
  }

  // 搜索历史记录
  static searchHistory(query: string): ConversionHistoryItem[] {
    const history = this.getHistory()
    const lowerQuery = query.toLowerCase()
    
    return history.filter(item =>
      item.fileName.toLowerCase().includes(lowerQuery) ||
      item.fromFormat.toLowerCase().includes(lowerQuery) ||
      item.toFormat.toLowerCase().includes(lowerQuery)
    )
  }

  // 获取统计信息
  static getStatistics(): {
    totalConversions: number
    successfulConversions: number
    failedConversions: number
    totalSizeSaved: number
    averageProcessingTime: number
    mostUsedFormats: { format: string; count: number }[]
    conversionsByDate: { date: string; count: number }[]
  } {
    const history = this.getHistory()
    
    const totalConversions = history.length
    const successfulConversions = history.filter(item => item.status === 'completed').length
    const failedConversions = history.filter(item => item.status === 'failed').length
    
    const totalSizeSaved = history
      .filter(item => item.status === 'completed')
      .reduce((total, item) => total + (item.originalSize - item.convertedSize), 0)
    
    const averageProcessingTime = history.length > 0
      ? history.reduce((total, item) => total + item.processingTime, 0) / history.length
      : 0

    // 统计最常用的转换格式
    const formatCounts: { [key: string]: number } = {}
    history.forEach(item => {
      const conversion = `${item.fromFormat} → ${item.toFormat}`
      formatCounts[conversion] = (formatCounts[conversion] || 0) + 1
    })
    
    const mostUsedFormats = Object.entries(formatCounts)
      .map(([format, count]) => ({ format, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5)

    // 按日期统计转换次数
    const dateCounts: { [key: string]: number } = {}
    history.forEach(item => {
      const dateKey = item.timestamp.toISOString().split('T')[0]
      dateCounts[dateKey] = (dateCounts[dateKey] || 0) + 1
    })
    
    const conversionsByDate = Object.entries(dateCounts)
      .map(([date, count]) => ({ date, count }))
      .sort((a, b) => a.date.localeCompare(b.date))
      .slice(-30) // 最近30天

    return {
      totalConversions,
      successfulConversions,
      failedConversions,
      totalSizeSaved,
      averageProcessingTime,
      mostUsedFormats,
      conversionsByDate
    }
  }

  // 导出历史记录为JSON
  static exportHistory(): string {
    const history = this.getHistory()
    return JSON.stringify(history, null, 2)
  }

  // 导入历史记录
  static importHistory(jsonData: string): boolean {
    try {
      const importedHistory = JSON.parse(jsonData) as ConversionHistoryItem[]
      
      // 验证数据格式
      if (!Array.isArray(importedHistory)) {
        throw new Error('无效的历史记录格式')
      }

      // 合并现有历史记录
      const currentHistory = this.getHistory()
      const mergedHistory = [...importedHistory, ...currentHistory]
      
      // 去重（基于文件名和时间戳）
      const uniqueHistory = mergedHistory.filter((item, index, array) => 
        array.findIndex(other => 
          other.fileName === item.fileName && 
          other.timestamp.getTime() === new Date(item.timestamp).getTime()
        ) === index
      )

      // 限制数量并保存
      const limitedHistory = uniqueHistory
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
        .slice(0, this.MAX_HISTORY_ITEMS)

      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(limitedHistory))
      return true
    } catch (error) {
      console.error('导入历史记录失败:', error)
      return false
    }
  }

  // 获取存储使用情况
  static getStorageInfo(): {
    used: number
    available: number
    percentage: number
  } {
    try {
      const historyData = localStorage.getItem(this.STORAGE_KEY) || ''
      const used = new Blob([historyData]).size
      const available = 5 * 1024 * 1024 // 假设localStorage限制为5MB
      const percentage = (used / available) * 100

      return { used, available, percentage }
    } catch (error) {
      console.error('获取存储信息失败:', error)
      return { used: 0, available: 0, percentage: 0 }
    }
  }

  // 清理过期的历史记录
  static cleanupOldHistory(daysToKeep: number = 30): number {
    try {
      const history = this.getHistory()
      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep)

      const filteredHistory = history.filter(item => item.timestamp >= cutoffDate)
      const removedCount = history.length - filteredHistory.length

      if (removedCount > 0) {
        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(filteredHistory))
      }

      return removedCount
    } catch (error) {
      console.error('清理历史记录失败:', error)
      return 0
    }
  }
}
