export class ImageService {
  // 支持的图片格式
  static readonly SUPPORTED_FORMATS = ['jpg', 'jpeg', 'png', 'webp', 'svg', 'gif', 'bmp']
  
  // 图片格式转换
  static async convertFormat(
    file: File, 
    targetFormat: string, 
    quality: number = 0.9
  ): Promise<File> {
    try {
      // SVG特殊处理
      if (file.type === 'image/svg+xml') {
        return this.convertSvg(file, targetFormat, quality)
      }

      // 其他格式转换
      const canvas = await this.fileToCanvas(file)
      const blob = await this.canvasToBlob(canvas, targetFormat, quality)
      
      const fileName = this.changeFileExtension(file.name, targetFormat)
      return new File([blob], fileName, { type: `image/${targetFormat}` })
    } catch (error) {
      console.error('图片格式转换失败:', error)
      throw new Error('图片格式转换失败')
    }
  }

  // 图片压缩
  static async compressImage(
    file: File, 
    quality: number = 0.8, 
    maxWidth?: number, 
    maxHeight?: number
  ): Promise<File> {
    try {
      const canvas = await this.fileToCanvas(file)
      
      // 如果指定了最大尺寸，进行缩放
      if (maxWidth || maxHeight) {
        const resizedCanvas = this.resizeCanvas(canvas, maxWidth, maxHeight)
        const blob = await this.canvasToBlob(resizedCanvas, 'jpeg', quality)
        const fileName = this.changeFileExtension(file.name, 'jpg')
        return new File([blob], fileName, { type: 'image/jpeg' })
      }

      const blob = await this.canvasToBlob(canvas, 'jpeg', quality)
      const fileName = this.changeFileExtension(file.name, 'jpg')
      return new File([blob], fileName, { type: 'image/jpeg' })
    } catch (error) {
      console.error('图片压缩失败:', error)
      throw new Error('图片压缩失败')
    }
  }

  // 图片缩放
  static async resizeImage(
    file: File, 
    width: number, 
    height: number, 
    maintainAspectRatio: boolean = true
  ): Promise<File> {
    try {
      const canvas = await this.fileToCanvas(file)
      const resizedCanvas = this.resizeCanvas(canvas, width, height, maintainAspectRatio)
      
      const originalFormat = file.type.split('/')[1]
      const blob = await this.canvasToBlob(resizedCanvas, originalFormat, 0.9)
      
      const fileName = file.name.replace(/\.[^/.]+$/, `_${width}x${height}.$&`)
      return new File([blob], fileName, { type: file.type })
    } catch (error) {
      console.error('图片缩放失败:', error)
      throw new Error('图片缩放失败')
    }
  }

  // 图片旋转
  static async rotateImage(file: File, degrees: number): Promise<File> {
    try {
      const canvas = await this.fileToCanvas(file)
      const rotatedCanvas = this.rotateCanvas(canvas, degrees)
      
      const originalFormat = file.type.split('/')[1]
      const blob = await this.canvasToBlob(rotatedCanvas, originalFormat, 0.9)
      
      const fileName = file.name.replace(/\.[^/.]+$/, `_rotated.$&`)
      return new File([blob], fileName, { type: file.type })
    } catch (error) {
      console.error('图片旋转失败:', error)
      throw new Error('图片旋转失败')
    }
  }

  // 添加水印
  static async addWatermark(
    file: File, 
    watermarkText: string, 
    options: {
      position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'center'
      fontSize?: number
      color?: string
      opacity?: number
    } = {}
  ): Promise<File> {
    try {
      const canvas = await this.fileToCanvas(file)
      const ctx = canvas.getContext('2d')
      if (!ctx) throw new Error('无法获取canvas上下文')

      const {
        position = 'bottom-right',
        fontSize = 20,
        color = 'white',
        opacity = 0.7
      } = options

      // 设置文字样式
      ctx.font = `${fontSize}px Arial`
      ctx.fillStyle = color
      ctx.globalAlpha = opacity

      // 计算文字位置
      const textMetrics = ctx.measureText(watermarkText)
      const textWidth = textMetrics.width
      const textHeight = fontSize

      let x: number, y: number
      switch (position) {
        case 'top-left':
          x = 10
          y = textHeight + 10
          break
        case 'top-right':
          x = canvas.width - textWidth - 10
          y = textHeight + 10
          break
        case 'bottom-left':
          x = 10
          y = canvas.height - 10
          break
        case 'bottom-right':
          x = canvas.width - textWidth - 10
          y = canvas.height - 10
          break
        case 'center':
          x = (canvas.width - textWidth) / 2
          y = canvas.height / 2
          break
        default:
          x = canvas.width - textWidth - 10
          y = canvas.height - 10
      }

      ctx.fillText(watermarkText, x, y)

      const originalFormat = file.type.split('/')[1]
      const blob = await this.canvasToBlob(canvas, originalFormat, 0.9)
      
      const fileName = file.name.replace(/\.[^/.]+$/, `_watermarked.$&`)
      return new File([blob], fileName, { type: file.type })
    } catch (error) {
      console.error('添加水印失败:', error)
      throw new Error('添加水印失败')
    }
  }

  // 获取图片信息
  static async getImageInfo(file: File): Promise<{
    width: number
    height: number
    size: number
    format: string
    aspectRatio: number
  }> {
    try {
      const canvas = await this.fileToCanvas(file)
      return {
        width: canvas.width,
        height: canvas.height,
        size: file.size,
        format: file.type.split('/')[1],
        aspectRatio: canvas.width / canvas.height
      }
    } catch (error) {
      console.error('获取图片信息失败:', error)
      throw new Error('获取图片信息失败')
    }
  }

  // 辅助方法：文件转Canvas
  private static async fileToCanvas(file: File): Promise<HTMLCanvasElement> {
    return new Promise((resolve, reject) => {
      const img = new Image()
      img.onload = () => {
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')
        if (!ctx) {
          reject(new Error('无法获取canvas上下文'))
          return
        }

        canvas.width = img.width
        canvas.height = img.height
        ctx.drawImage(img, 0, 0)
        resolve(canvas)
      }
      img.onerror = () => reject(new Error('图片加载失败'))
      img.src = URL.createObjectURL(file)
    })
  }

  // 辅助方法：Canvas转Blob
  private static async canvasToBlob(
    canvas: HTMLCanvasElement, 
    format: string, 
    quality: number
  ): Promise<Blob> {
    return new Promise((resolve) => {
      const mimeType = format === 'jpg' ? 'image/jpeg' : `image/${format}`
      canvas.toBlob((blob) => resolve(blob!), mimeType, quality)
    })
  }

  // 辅助方法：缩放Canvas
  private static resizeCanvas(
    canvas: HTMLCanvasElement, 
    maxWidth?: number, 
    maxHeight?: number, 
    maintainAspectRatio: boolean = true
  ): HTMLCanvasElement {
    const newCanvas = document.createElement('canvas')
    const ctx = newCanvas.getContext('2d')
    if (!ctx) throw new Error('无法获取canvas上下文')

    let { width, height } = canvas

    if (maxWidth || maxHeight) {
      if (maintainAspectRatio) {
        const aspectRatio = width / height
        
        if (maxWidth && maxHeight) {
          if (width > height) {
            width = Math.min(width, maxWidth)
            height = width / aspectRatio
            if (height > maxHeight) {
              height = maxHeight
              width = height * aspectRatio
            }
          } else {
            height = Math.min(height, maxHeight)
            width = height * aspectRatio
            if (width > maxWidth) {
              width = maxWidth
              height = width / aspectRatio
            }
          }
        } else if (maxWidth) {
          width = Math.min(width, maxWidth)
          height = width / aspectRatio
        } else if (maxHeight) {
          height = Math.min(height, maxHeight)
          width = height * aspectRatio
        }
      } else {
        width = maxWidth || width
        height = maxHeight || height
      }
    }

    newCanvas.width = width
    newCanvas.height = height
    ctx.drawImage(canvas, 0, 0, width, height)
    
    return newCanvas
  }

  // 辅助方法：旋转Canvas
  private static rotateCanvas(canvas: HTMLCanvasElement, degrees: number): HTMLCanvasElement {
    const newCanvas = document.createElement('canvas')
    const ctx = newCanvas.getContext('2d')
    if (!ctx) throw new Error('无法获取canvas上下文')

    const radians = (degrees * Math.PI) / 180
    const sin = Math.abs(Math.sin(radians))
    const cos = Math.abs(Math.cos(radians))

    newCanvas.width = canvas.width * cos + canvas.height * sin
    newCanvas.height = canvas.width * sin + canvas.height * cos

    ctx.translate(newCanvas.width / 2, newCanvas.height / 2)
    ctx.rotate(radians)
    ctx.drawImage(canvas, -canvas.width / 2, -canvas.height / 2)

    return newCanvas
  }

  // 辅助方法：SVG转换
  private static async convertSvg(file: File, targetFormat: string, quality: number): Promise<File> {
    const svgText = await file.text()
    const canvas = await this.svgToCanvas(svgText)
    const blob = await this.canvasToBlob(canvas, targetFormat, quality)
    
    const fileName = this.changeFileExtension(file.name, targetFormat)
    return new File([blob], fileName, { type: `image/${targetFormat}` })
  }

  // 辅助方法：SVG转Canvas
  private static async svgToCanvas(svgText: string): Promise<HTMLCanvasElement> {
    return new Promise((resolve, reject) => {
      const img = new Image()
      img.onload = () => {
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')
        if (!ctx) {
          reject(new Error('无法获取canvas上下文'))
          return
        }

        canvas.width = img.width || 800
        canvas.height = img.height || 600
        ctx.drawImage(img, 0, 0)
        resolve(canvas)
      }
      img.onerror = () => reject(new Error('SVG加载失败'))
      
      const blob = new Blob([svgText], { type: 'image/svg+xml' })
      img.src = URL.createObjectURL(blob)
    })
  }

  // 辅助方法：更改文件扩展名
  private static changeFileExtension(fileName: string, newExtension: string): string {
    return fileName.replace(/\.[^/.]+$/, `.${newExtension}`)
  }
}
